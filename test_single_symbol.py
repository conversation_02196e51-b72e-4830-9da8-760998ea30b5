#!/usr/bin/env python3
"""
Test script to load data for a single symbol (NIFTY50-INDEX) to verify the fix.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings
from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.market_data_service import MarketDataService
from app.database.models import MarketType

logger = get_logger(__name__)

def test_single_symbol():
    """Test loading data for NIFTY50-INDEX."""
    try:
        logger.info("🧪 Testing single symbol data loading...")
        
        # Initialize services
        logger.info("Initializing services...")
        db = next(get_db())
        market_data_service = MarketDataService(db)

        # Initialize Fyers connection
        logger.info("Initializing Fyers API connection...")
        fyers_success = market_data_service.initialize_fyers_connection()
        if not fyers_success:
            logger.error("❌ Failed to initialize Fyers API connection")
            return False
        logger.info("✅ Fyers API connection established")
        
        # Test symbol - use the correct Fyers format
        symbol = "NSE:NIFTY50-INDEX"  # Correct Fyers symbol format
        db_symbol_name = "NIFTY50"  # Database symbol name
        logger.info(f"Testing symbol: {symbol} (DB: {db_symbol_name})")
        
        # Get or create symbol
        logger.info(f"Getting or creating symbol: {db_symbol_name}")
        db_symbol = market_data_service.data_service.get_symbol_by_name(db_symbol_name)

        if not db_symbol:
            # Create symbol if it doesn't exist
            symbol_data = {
                'symbol': db_symbol_name,
                'name': 'NIFTY 50 Index',
                'market_type': MarketType.INDEX,
                'exchange': 'NSE',
                'token': symbol,  # Use Fyers symbol as token
                'lot_size': 1,
                'tick_size': 0.05,
                'is_active': True
            }
            db_symbol = market_data_service.data_service.create_symbol(symbol_data)

        if db_symbol:
            logger.info(f"✅ Symbol created/found: {db_symbol.symbol} (ID: {db_symbol.id})")
        else:
            logger.error(f"❌ Failed to create/find symbol: {db_symbol_name}")
            return False
        
        # Test loading a larger amount of data (last 30 days to ensure we get some data)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        logger.info(f"Loading data from {start_date.date()} to {end_date.date()}")
        
        # Test direct Fyers API call first
        logger.info("Testing direct Fyers API call...")
        fyers_data = market_data_service.fyers_client.get_historical_data(
            symbol=symbol,
            timeframe="1",
            start_date=start_date,
            end_date=end_date
        )
        logger.info(f"Direct Fyers API returned {len(fyers_data)} records")

        # Load historical data
        success = market_data_service.fetch_and_store_historical_data(
            symbol=symbol,
            timeframe="1",  # 1 minute
            start_date=start_date,
            end_date=end_date
        )

        if success:
            logger.info(f"✅ Successfully loaded data for {symbol}")

            # Show latest 5 records using data service
            latest_data = market_data_service.data_service.get_latest_ohlcv(db_symbol_name, count=5)
            if latest_data:
                logger.info("📈 Latest 5 OHLCV records:")
                for i, record in enumerate(latest_data, 1):
                    logger.info(f"  {i}. {record.datetime} | O:{record.open} H:{record.high} L:{record.low} C:{record.close} V:{record.volume}")

            return True
        else:
            logger.error(f"❌ Failed to load data for {db_symbol_name}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    logger.info("🚀 Starting single symbol test...")
    success = test_single_symbol()
    
    if success:
        logger.info("✅ Test completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Test failed!")
        sys.exit(1)

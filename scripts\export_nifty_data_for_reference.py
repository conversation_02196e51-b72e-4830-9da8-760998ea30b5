#!/usr/bin/env python3
"""
Export NIFTY data from TimescaleDB to CSV format for reference project testing.
"""

import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.optimized_data_service import OptimizedDataService

logger = get_logger(__name__)


def export_nifty_data_to_csv(
    output_path: str,
    symbol: str = "NIFTY",
    start_date: str = "2015-01-01",
    end_date: str = "2025-12-31",
    interval: str = "1m"
):
    """
    Export NIFTY data from database to CSV format.
    
    Args:
        output_path: Path to save the CSV file
        symbol: Symbol to export (default: NIFTY)
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        interval: Data interval (default: 1m)
    """
    try:
        logger.info(f"🚀 Starting NIFTY data export to {output_path}")
        
        # Initialize database connection
        db = next(get_db())
        data_service = OptimizedDataService(db)
        
        # Convert date strings to datetime objects
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        logger.info(f"📊 Fetching {symbol} data from {start_date} to {end_date}")
        
        # Get data from database
        df = data_service.get_ohlcv_data(
            symbol=symbol,
            start_time=start_dt,
            end_time=end_dt,
            interval=interval,
            exchange="NSE",
            as_dataframe=True
        )
        
        if df is None or df.empty:
            logger.error(f"❌ No data found for {symbol} in the specified date range")
            return False
        
        logger.info(f"✅ Retrieved {len(df)} records")
        logger.info(f"📅 Data range: {df.index.min()} to {df.index.max()}")
        
        # Prepare data for reference project format
        # Reset index to make datetime a column
        df_export = df.reset_index()
        
        # Rename columns to match reference project expectations
        df_export = df_export.rename(columns={
            'datetime': 'date',
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # Ensure proper column order
        df_export = df_export[['date', 'Open', 'High', 'Low', 'Close', 'Volume']]
        
        # Format the date column
        df_export['date'] = pd.to_datetime(df_export['date']).dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Round price columns to 2 decimal places
        price_columns = ['Open', 'High', 'Low', 'Close']
        for col in price_columns:
            df_export[col] = df_export[col].round(2)
        
        # Create output directory if it doesn't exist
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Export to CSV
        df_export.to_csv(output_path, index=False)
        
        logger.info(f"✅ Successfully exported {len(df_export)} records to {output_path}")
        logger.info(f"📊 Sample data:")
        logger.info(f"{df_export.head()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error exporting NIFTY data: {e}")
        return False
    finally:
        if 'db' in locals():
            db.close()


def check_data_availability():
    """Check what NIFTY data is available in the database."""
    try:
        logger.info("🔍 Checking NIFTY data availability...")
        
        db = next(get_db())
        data_service = OptimizedDataService(db)
        
        # Get latest data to check availability
        latest_data = data_service.get_latest_ohlcv("NIFTY", count=5)
        
        if latest_data:
            logger.info("✅ NIFTY data found in database:")
            for i, record in enumerate(latest_data):
                logger.info(f"  {i+1}. {record['datetime']}: O={record['open']:.2f}, "
                           f"H={record['high']:.2f}, L={record['low']:.2f}, "
                           f"C={record['close']:.2f}, V={record['volume']}")
            return True
        else:
            logger.warning("⚠️  No NIFTY data found in database")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking data availability: {e}")
        return False
    finally:
        if 'db' in locals():
            db.close()


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Export NIFTY data for reference project')
    parser.add_argument('--output', '-o', 
                       default='C:/Users/<USER>/Desktop/Python/data/NIFTY_1min.csv',
                       help='Output CSV file path')
    parser.add_argument('--symbol', '-s', default='NIFTY',
                       help='Symbol to export (default: NIFTY)')
    parser.add_argument('--start-date', default='2015-01-01',
                       help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', default='2025-12-31',
                       help='End date (YYYY-MM-DD)')
    parser.add_argument('--check', action='store_true',
                       help='Check data availability only')
    
    args = parser.parse_args()
    
    if args.check:
        check_data_availability()
        return
    
    # Export data
    success = export_nifty_data_to_csv(
        output_path=args.output,
        symbol=args.symbol,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    if success:
        logger.info("🎉 Data export completed successfully!")
    else:
        logger.error("❌ Data export failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()

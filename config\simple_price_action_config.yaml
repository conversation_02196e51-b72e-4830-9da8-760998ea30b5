# Simple Price Action Strategy Configuration
# Integrated version for the backtesting framework

backtest:
  initial_capital: 100000
  commission: 0.001  # 0.1% commission
  slippage: 0.001    # 0.1% slippage

data:
  symbol: 'RELIANCE'
  start_date: '2024-01-01'
  end_date: '2024-12-31'
  timeframe: 30  # minutes (1 for 1-minute, 5 for 5-minute, etc.)

strategy:
  # Time settings
  market_start_time: '09:15'
  market_end_time: '15:30'
  entry_start_time: '09:30'
  exit_end_time: '15:15'
  force_exit_time: '15:15'
  stop_new_trades_time: '15:00'
  
  # Indicator periods
  atr_period: 10
  fast_ma_period: 5
  slow_ma_period: 21
  rsi_period: 10
  rsi_overbought: 70
  rsi_oversold: 30
  
  # Risk management
  sl_atr_multiplier: 2.0
  tp_rr: 2.0  # Take profit risk/reward ratio
  position_size: 0.1  # 10% of capital per trade
  
  # Price action settings
  signal_threshold: 0.15
  volatility_check_multiplier: 0.55
  weight_breakout: 3.0
  weight_engulfing: 3.0
  weight_pinbar: 3.0
  weight_inside_bar: 0.5
  weight_outside_bar: 2.5
  
  # Entry filters
  enable_advanced_filters: true
  enable_time_filter: true
  enable_trend_filter: true
  enable_volatility_filter: true
  trend_filter_period: 13
  volatility_multiplier: 1.22

# Pattern recognition settings
price_action_patterns:
  engulfing:
    threshold: 0.0
  pin_bars:
    body_ratio: 0.35
    tail_ratio: 0.5
  inside_bar:
    enabled: true
  outside_bar:
    enabled: true

# Output settings
output:
  save_trades: true
  trades_file: 'backtest_trades.csv'
  save_equity_curve: true
  equity_curve_file: 'equity_curve.csv'
  save_metrics: true
  metrics_file: 'backtest_metrics.json'

# Logging settings
logging:
  level: 'INFO'
  enable_debug: false
  enable_detailed_metrics: true

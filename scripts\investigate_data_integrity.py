#!/usr/bin/env python3
"""
Investigate data integrity issues in load_all_symbols_15year_data.py
Check for discrepancies between logged record count and actual DB records.
"""

import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger(__name__)

def investigate_data_integrity():
    """
    Investigate data integrity issues by:
    1. Checking record counts per symbol
    2. Looking for duplicate timestamps
    3. Analyzing data gaps
    4. Checking for failed transactions
    """
    database_settings = get_settings().database
    engine = create_engine(database_settings.url)
    
    logger.info("🔍 Starting data integrity investigation...")
    
    try:
        with engine.connect() as conn:
            # Step 1: Get overall statistics
            logger.info("📊 Getting overall database statistics...")
            
            total_records = conn.execute(text("SELECT COUNT(*) FROM stock_ohlcv;")).scalar()
            logger.info(f"Total records in database: {total_records:,}")
            
            # Get unique symbols count
            unique_symbols = conn.execute(text("SELECT COUNT(DISTINCT symbol) FROM stock_ohlcv;")).scalar()
            logger.info(f"Unique symbols: {unique_symbols:,}")
            
            # Step 2: Check record counts per symbol
            logger.info("\n📋 Record counts per symbol:")
            symbol_counts = conn.execute(text("""
                SELECT symbol, COUNT(*) as record_count
                FROM stock_ohlcv 
                GROUP BY symbol 
                ORDER BY record_count DESC;
            """))
            
            for symbol, count in symbol_counts:
                logger.info(f"   {symbol}: {count:,} records")
            
            # Step 3: Check for duplicate timestamps within symbols
            logger.info("\n🔍 Checking for duplicate timestamps...")
            duplicates = conn.execute(text("""
                SELECT symbol, datetime, COUNT(*) as duplicate_count
                FROM stock_ohlcv 
                GROUP BY symbol, datetime 
                HAVING COUNT(*) > 1
                ORDER BY duplicate_count DESC, symbol, datetime;
            """))
            
            duplicate_found = False
            for symbol, dt, dup_count in duplicates:
                if not duplicate_found:
                    logger.info("❌ Found duplicate timestamps:")
                    duplicate_found = True
                logger.info(f"   {symbol} at {dt}: {dup_count} duplicates")
            
            if not duplicate_found:
                logger.info("✅ No duplicate timestamps found")
            
            # Step 4: Check for data gaps in RELIANCE (focus on the problematic symbol)
            logger.info("\n🔍 Analyzing RELIANCE data gaps...")
            
            reliance_stats = conn.execute(text("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(datetime) as first_timestamp,
                    MAX(datetime) as last_timestamp,
                    COUNT(DISTINCT DATE(datetime)) as unique_days
                FROM stock_ohlcv 
                WHERE symbol = 'RELIANCE';
            """)).fetchone()
            
            if reliance_stats:
                total_records, first_ts, last_ts, unique_days = reliance_stats
                logger.info(f"RELIANCE statistics:")
                logger.info(f"   Total records: {total_records:,}")
                logger.info(f"   Date range: {first_ts} to {last_ts}")
                logger.info(f"   Unique days: {unique_days:,}")
                
                # Calculate expected records (assuming 375 minutes per trading day)
                expected_records = unique_days * 375  # Approximate trading minutes per day
                logger.info(f"   Expected records (approx): {expected_records:,}")
                logger.info(f"   Difference: {expected_records - total_records:,}")
            
            # Step 5: Check for specific date ranges with missing data
            logger.info("\n📅 Checking for days with unusually low record counts...")
            
            daily_counts = conn.execute(text("""
                SELECT 
                    DATE(datetime) as trading_date,
                    COUNT(*) as record_count
                FROM stock_ohlcv 
                WHERE symbol = 'RELIANCE'
                GROUP BY DATE(datetime)
                HAVING COUNT(*) < 300  -- Less than expected trading minutes
                ORDER BY trading_date DESC
                LIMIT 10;
            """))
            
            low_count_days = list(daily_counts)
            if low_count_days:
                logger.info("📉 Days with low record counts:")
                for date, count in low_count_days:
                    logger.info(f"   {date}: {count} records")
            else:
                logger.info("✅ No days with unusually low record counts")
            
            # Step 6: Check for timezone-related issues
            logger.info("\n🕐 Checking for timezone-related patterns...")
            
            # Check if there are records at unusual hours (might indicate timezone issues)
            hour_distribution = conn.execute(text("""
                SELECT 
                    EXTRACT(HOUR FROM datetime) as hour,
                    COUNT(*) as record_count
                FROM stock_ohlcv 
                WHERE symbol = 'RELIANCE'
                GROUP BY EXTRACT(HOUR FROM datetime)
                ORDER BY hour;
            """))
            
            logger.info("⏰ Hour distribution for RELIANCE:")
            for hour, count in hour_distribution:
                logger.info(f"   Hour {int(hour):02d}: {count:,} records")
            
            # Step 7: Check for recent data loading patterns
            logger.info("\n📈 Recent data loading patterns...")
            
            recent_data = conn.execute(text("""
                SELECT 
                    DATE(created_at) as load_date,
                    COUNT(*) as records_loaded
                FROM stock_ohlcv 
                WHERE symbol = 'RELIANCE'
                GROUP BY DATE(created_at)
                ORDER BY load_date DESC
                LIMIT 5;
            """))
            
            logger.info("📅 Recent loading activity:")
            for load_date, count in recent_data:
                logger.info(f"   {load_date}: {count:,} records loaded")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to investigate data integrity: {e}")
        return False

def check_upsert_behavior():
    """
    Test the upsert behavior to understand why records might be missing.
    """
    database_settings = get_settings().database
    engine = create_engine(database_settings.url)
    
    logger.info("\n🧪 Testing upsert behavior...")
    
    try:
        with engine.connect() as conn:
            # Check if there are any constraint violations or conflicts
            logger.info("🔍 Checking for potential constraint violations...")
            
            # Look for records that might have been rejected due to constraints
            constraint_check = conn.execute(text("""
                SELECT 
                    symbol,
                    interval,
                    COUNT(*) as record_count,
                    COUNT(DISTINCT datetime) as unique_timestamps
                FROM stock_ohlcv 
                WHERE symbol = 'RELIANCE'
                GROUP BY symbol, interval;
            """))
            
            for symbol, interval, total_count, unique_count in constraint_check:
                logger.info(f"   {symbol} ({interval}): {total_count:,} total, {unique_count:,} unique timestamps")
                if total_count != unique_count:
                    logger.warning(f"   ⚠️  Potential duplicate issue: {total_count - unique_count} duplicates")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to check upsert behavior: {e}")
        return False

def main():
    """Main execution function."""
    try:
        logger.info("🚀 Starting data integrity investigation...")
        
        success = investigate_data_integrity()
        if success:
            check_upsert_behavior()
            logger.info("\n🎉 Data integrity investigation completed!")
        else:
            logger.error("❌ Data integrity investigation failed!")
            return False
        
        return True
    except Exception as e:
        logger.error(f"❌ Script execution failed: {e}")
        return False

if __name__ == "__main__":
    main()

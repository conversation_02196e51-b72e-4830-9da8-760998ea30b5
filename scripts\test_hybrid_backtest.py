#!/usr/bin/env python3
"""
Test script to validate that the hybrid backtesting framework produces
identical results to the reference implementation.
"""

import sys
import json
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.backtest_service import BacktestService
from app.services.backtesting.hybrid_engine import HybridBacktestEngine

logger = get_logger(__name__)


def run_reference_backtest(timeframe: int = 30) -> dict:
    """Run the reference backtest for comparison."""
    logger.info("🔬 Running reference backtest...")
    
    reference_dir = project_root / "Reference" / "V7_IntradayCleanup_Best_30Min"
    sys.path.insert(0, str(reference_dir))
    
    import os
    original_cwd = os.getcwd()
    os.chdir(str(reference_dir))
    
    try:
        from SimplePriceActionStrategyMain_DB import main_db_version
        
        # Run reference backtest
        result = main_db_version(timeframe=timeframe)
        
        return {
            'execution_time': result['execution_time'],
            'total_trades': result['total_trades'],
            'total_return': result['total_return'],
            'custom_metrics': result['custom_metrics'],
            'backtest_stats': result['backtest_stats']
        }
        
    finally:
        os.chdir(original_cwd)


def run_hybrid_backtest(timeframe: int = 30) -> dict:
    """Run the hybrid backtest."""
    logger.info("🔬 Running hybrid backtest...")
    
    try:
        db = next(get_db())
        hybrid_engine = HybridBacktestEngine(db)
        
        # Run hybrid backtest
        result = hybrid_engine.run_backtest(
            symbol="NIFTY50",
            timeframe=timeframe,
            initial_capital=30000,
            margin=0.1,
            commission=0.0
        )
        
        hybrid_engine.close()
        return result
        
    except Exception as e:
        logger.error(f"❌ Hybrid backtest failed: {e}")
        raise


def compare_results(reference_result: dict, hybrid_result: dict) -> dict:
    """Compare results between reference and hybrid implementations."""
    logger.info("\n" + "="*80)
    logger.info(" "*25 + "METRICS VALIDATION RESULTS")
    logger.info("="*80)
    
    comparison = {
        'metrics_match': True,
        'discrepancies': [],
        'validation_details': {}
    }
    
    try:
        # Extract metrics for comparison
        ref_custom = reference_result['custom_metrics']
        hybrid_custom = hybrid_result['custom_metrics']
        
        # Key metrics to validate
        key_metrics = [
            'Total Trades',
            'Winning Trades',
            'Losing Trades',
            'Win Rate (%)',
            'Total Return (%)',
            'Annualized Return (%)',
            'Max Drawdown (%)',
            'Profit Factor',
            'Sharpe Ratio',
            'Sortino Ratio'
        ]
        
        logger.info("\n📊 METRICS COMPARISON:")
        logger.info(f"{'Metric':<25} | {'Reference':<15} | {'Hybrid':<15} | {'Match':<8} | {'Diff':<10}")
        logger.info("-" * 85)
        
        for metric in key_metrics:
            ref_value = ref_custom.get(metric, 0)
            hybrid_value = hybrid_custom.get(metric, 0)
            
            # Calculate difference
            if isinstance(ref_value, (int, float)) and isinstance(hybrid_value, (int, float)):
                if ref_value != 0:
                    diff_pct = abs((hybrid_value - ref_value) / ref_value) * 100
                else:
                    diff_pct = abs(hybrid_value) * 100
                
                # Consider match if difference is less than 0.01%
                is_match = diff_pct < 0.01
                match_symbol = "✅" if is_match else "❌"
                
                if not is_match:
                    comparison['metrics_match'] = False
                    comparison['discrepancies'].append({
                        'metric': metric,
                        'reference': ref_value,
                        'hybrid': hybrid_value,
                        'difference_pct': diff_pct
                    })
                
                logger.info(f"{metric:<25} | {ref_value:<15.2f} | {hybrid_value:<15.2f} | {match_symbol:<8} | {diff_pct:<10.2f}%")
                
                comparison['validation_details'][metric] = {
                    'reference': ref_value,
                    'hybrid': hybrid_value,
                    'difference_pct': diff_pct,
                    'match': is_match
                }
            else:
                # String comparison
                is_match = str(ref_value) == str(hybrid_value)
                match_symbol = "✅" if is_match else "❌"
                
                if not is_match:
                    comparison['metrics_match'] = False
                    comparison['discrepancies'].append({
                        'metric': metric,
                        'reference': ref_value,
                        'hybrid': hybrid_value,
                        'difference': 'String mismatch'
                    })
                
                logger.info(f"{metric:<25} | {str(ref_value):<15} | {str(hybrid_value):<15} | {match_symbol:<8} | {'N/A':<10}")
        
        # Performance comparison
        logger.info("\n⏱️  PERFORMANCE COMPARISON:")
        ref_time = reference_result['execution_time']
        hybrid_time = hybrid_result['execution_time']
        speedup = ref_time / hybrid_time if hybrid_time > 0 else 1.0
        
        logger.info(f"Reference execution time: {ref_time:.2f} seconds")
        logger.info(f"Hybrid execution time:    {hybrid_time:.2f} seconds")
        logger.info(f"Speedup factor:           {speedup:.2f}x")
        
        comparison['performance'] = {
            'reference_time': ref_time,
            'hybrid_time': hybrid_time,
            'speedup_factor': speedup
        }
        
        # Overall validation result
        if comparison['metrics_match']:
            logger.info("\n🎉 VALIDATION PASSED: All metrics match!")
        else:
            logger.info(f"\n⚠️  VALIDATION ISSUES: {len(comparison['discrepancies'])} discrepancies found")
            for disc in comparison['discrepancies']:
                logger.info(f"   - {disc['metric']}: {disc.get('difference_pct', disc.get('difference', 'Unknown'))}")
        
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"❌ Comparison failed: {e}")
        comparison['error'] = str(e)
    
    return comparison


def save_validation_report(
    reference_result: dict,
    hybrid_result: dict,
    comparison: dict,
    timeframe: int
):
    """Save validation report to file."""
    report = {
        'timestamp': datetime.utcnow().isoformat(),
        'timeframe': timeframe,
        'reference_result': reference_result,
        'hybrid_result': hybrid_result,
        'comparison': comparison,
        'validation_passed': comparison.get('metrics_match', False)
    }
    
    filename = f"hybrid_validation_report_{timeframe}min.json"
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger.info(f"📄 Validation report saved: {filename}")
    return filename


def main():
    """Main validation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate hybrid backtesting framework')
    parser.add_argument('--timeframe', '-t', type=int, default=30, 
                       help='Timeframe in minutes (default: 30)')
    parser.add_argument('--save-report', action='store_true',
                       help='Save detailed validation report')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Hybrid Backtesting Framework Validation")
    logger.info(f"📊 Timeframe: {args.timeframe} minutes")
    
    try:
        # Run reference backtest
        logger.info("\n" + "="*50)
        logger.info("STEP 1: Running Reference Backtest")
        logger.info("="*50)
        reference_result = run_reference_backtest(args.timeframe)
        
        # Run hybrid backtest
        logger.info("\n" + "="*50)
        logger.info("STEP 2: Running Hybrid Backtest")
        logger.info("="*50)
        hybrid_result = run_hybrid_backtest(args.timeframe)
        
        # Compare results
        logger.info("\n" + "="*50)
        logger.info("STEP 3: Comparing Results")
        logger.info("="*50)
        comparison = compare_results(reference_result, hybrid_result)
        
        # Save report if requested
        if args.save_report:
            logger.info("\n" + "="*50)
            logger.info("STEP 4: Saving Validation Report")
            logger.info("="*50)
            report_file = save_validation_report(
                reference_result, hybrid_result, comparison, args.timeframe
            )
        
        # Final result
        if comparison.get('metrics_match', False):
            logger.info("\n🎉 VALIDATION SUCCESSFUL: Hybrid framework produces identical results!")
            return 0
        else:
            logger.error("\n❌ VALIDATION FAILED: Metrics do not match reference implementation")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Validation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())

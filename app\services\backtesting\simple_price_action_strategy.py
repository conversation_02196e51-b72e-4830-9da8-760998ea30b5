"""
Simple Price Action Strategy integrated with the backtesting framework.
Based on the reference implementation from V7_IntradayCleanup_Best_30Min.
"""

import numpy as np
import pandas as pd
import yaml
from datetime import datetime, time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from app.core.logging import get_logger
from app.services.backtesting.strategy import BaseStrategy, TradingSignal, SignalType, PositionType
from app.services.backtesting.orders import Order, OrderType

logger = get_logger(__name__)


@dataclass
class PriceActionConfig:
    """Configuration for price action strategy."""
    
    # Time settings
    market_start_time: str = "09:15"
    market_end_time: str = "15:30"
    entry_start_time: str = "09:30"
    exit_end_time: str = "15:15"
    force_exit_time: str = "15:15"
    stop_new_trades_time: str = "15:00"
    
    # Indicator periods
    atr_period: int = 10
    fast_ma_period: int = 5
    slow_ma_period: int = 21
    rsi_period: int = 10
    rsi_overbought: float = 98
    rsi_oversold: float = 25
    
    # Risk management
    sl_atr_multiplier: float = 0.85
    tp_rr: float = 2.0
    position_size: float = 0.99
    
    # Price action settings
    signal_threshold: float = 0.15
    volatility_check_multiplier: float = 0.55
    weight_breakout: float = 3.0
    weight_engulfing: float = 3.0
    weight_pinbar: float = 3.0
    weight_inside_bar: float = 0.5
    weight_outside_bar: float = 2.5
    
    # Entry filters
    enable_advanced_filters: bool = True
    enable_time_filter: bool = True
    enable_trend_filter: bool = True
    enable_volatility_filter: bool = True
    trend_filter_period: int = 13
    volatility_multiplier: float = 1.22


class PriceActionPatterns:
    """Price action pattern detection methods."""
    
    @staticmethod
    def detect_engulfing(data: pd.DataFrame, index: int) -> Tuple[bool, str]:
        """Detect bullish/bearish engulfing patterns."""
        if index < 1:
            return False, ""
        
        current = data.iloc[index]
        previous = data.iloc[index - 1]
        
        # Bullish engulfing
        if (previous['close'] < previous['open'] and  # Previous red candle
            current['close'] > current['open'] and    # Current green candle
            current['open'] < previous['close'] and   # Current opens below previous close
            current['close'] > previous['open']):     # Current closes above previous open
            return True, "bullish_engulfing"

        # Bearish engulfing
        if (previous['close'] > previous['open'] and  # Previous green candle
            current['close'] < current['open'] and    # Current red candle
            current['open'] > previous['close'] and   # Current opens above previous close
            current['close'] < previous['open']):     # Current closes below previous open
            return True, "bearish_engulfing"
        
        return False, ""
    
    @staticmethod
    def detect_pinbar(data: pd.DataFrame, index: int, body_ratio: float = 0.35, tail_ratio: float = 0.5) -> Tuple[bool, str]:
        """Detect pin bar patterns."""
        if index < 0:
            return False, ""
        
        candle = data.iloc[index]
        high, low, open_price, close = candle['high'], candle['low'], candle['open'], candle['close']
        
        body_size = abs(close - open_price)
        total_range = high - low
        
        if total_range == 0:
            return False, ""
        
        upper_tail = high - max(open_price, close)
        lower_tail = min(open_price, close) - low
        
        # Check if body is small relative to total range
        if body_size / total_range > body_ratio:
            return False, ""
        
        # Bullish pin bar (hammer) - long lower tail
        if lower_tail / total_range >= tail_ratio and upper_tail / total_range < 0.1:
            return True, "bullish_pinbar"
        
        # Bearish pin bar (shooting star) - long upper tail
        if upper_tail / total_range >= tail_ratio and lower_tail / total_range < 0.1:
            return True, "bearish_pinbar"
        
        return False, ""
    
    @staticmethod
    def detect_inside_bar(data: pd.DataFrame, index: int) -> Tuple[bool, str]:
        """Detect inside bar patterns."""
        if index < 1:
            return False, ""
        
        current = data.iloc[index]
        previous = data.iloc[index - 1]
        
        # Inside bar: current high < previous high AND current low > previous low
        if (current['high'] < previous['high'] and current['low'] > previous['low']):
            return True, "inside_bar"
        
        return False, ""
    
    @staticmethod
    def detect_outside_bar(data: pd.DataFrame, index: int) -> Tuple[bool, str]:
        """Detect outside bar patterns."""
        if index < 1:
            return False, ""
        
        current = data.iloc[index]
        previous = data.iloc[index - 1]
        
        # Outside bar: current high > previous high AND current low < previous low
        if (current['high'] > previous['high'] and current['low'] < previous['low']):
            return True, "outside_bar"
        
        return False, ""


class TechnicalIndicators:
    """Technical indicator calculations."""
    
    @staticmethod
    def calculate_ema(series: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average."""
        return series.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def calculate_rsi(series: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index."""
        delta = series.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        # Handle division by zero
        rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """Calculate Average True Range."""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(period).mean()
        return atr


class SimplePriceActionStrategy(BaseStrategy):
    """
    Simple Price Action Strategy that combines basic indicators and price patterns.
    Integrated with the backtesting framework.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the strategy with configuration."""
        super().__init__(
            name="SimplePriceActionStrategy",
            parameters=config.get('strategy', {}) if config else {}
        )

        # Load configuration
        if config:
            self.config = PriceActionConfig(**config.get('strategy', {}))
        else:
            self.config = PriceActionConfig()

        # Initialize indicators
        self.indicators = TechnicalIndicators()
        self.patterns = PriceActionPatterns()

        # Strategy state
        self.current_position = None
        self.entry_price = None
        self.stop_loss = None
        self.take_profit = None
        self.trade_log = []
        self.data = None

        logger.info("SimplePriceActionStrategy initialized")

    def initialize(self, data: pd.DataFrame) -> None:
        """Initialize strategy with historical data."""
        self.data = data.copy()
        self.is_initialized = True

        # Calculate technical indicators
        self._calculate_indicators()

        logger.info(f"Strategy initialized with {len(data)} data points")
    
    def _calculate_indicators(self) -> None:
        """Calculate all technical indicators."""
        if self.data is None or len(self.data) < max(self.config.atr_period, self.config.slow_ma_period):
            return
        
        # Moving averages
        self.data['EMA_Fast'] = self.indicators.calculate_ema(self.data['close'], self.config.fast_ma_period)
        self.data['EMA_Slow'] = self.indicators.calculate_ema(self.data['close'], self.config.slow_ma_period)

        # RSI
        self.data['RSI'] = self.indicators.calculate_rsi(self.data['close'], self.config.rsi_period)

        # ATR
        self.data['ATR'] = self.indicators.calculate_atr(
            self.data['high'],
            self.data['low'],
            self.data['close'],
            self.config.atr_period
        )
    
    def generate_signals(self, data: pd.DataFrame, current_index: int) -> List[TradingSignal]:
        """Generate trading signals based on price action and indicators."""
        signals = []

        if not self._can_trade(current_index):
            return signals

        # Get current data
        current_data = data.iloc[current_index]
        current_time = current_data.name if hasattr(current_data.name, 'time') else None

        # Check for entry signals
        if self.current_position is None:
            signal = self._check_entry_signals(current_index)
            if signal:
                signals.append(signal)

        # Check for exit signals
        elif self.current_position is not None:
            signal = self._check_exit_signals(current_index)
            if signal:
                signals.append(signal)

        return signals
    
    def _can_trade(self, current_index: int) -> bool:
        """Check if trading is allowed at current time."""
        if current_index < max(self.config.atr_period, self.config.slow_ma_period):
            return False
        
        # Add time-based filters here if needed
        return True
    
    def _check_entry_signals(self, current_index: int) -> Optional[TradingSignal]:
        """Check for entry signals based on comprehensive price action analysis."""
        current_data = self.data.iloc[current_index]

        # Skip if not enough data for indicators
        if current_index < max(self.config.atr_period, self.config.slow_ma_period):
            return None

        # Get current indicator values
        current_rsi = self.data['RSI'].iloc[current_index]
        current_atr = self.data['ATR'].iloc[current_index]
        fast_ma = self.data['EMA_Fast'].iloc[current_index]
        slow_ma = self.data['EMA_Slow'].iloc[current_index]

        # Check time filters
        if not self._is_valid_entry_time(current_data.name):
            return None

        # Calculate price action signal strength
        signal_strength = self._calculate_signal_strength(current_index)

        if signal_strength < self.config.signal_threshold:
            return None

        # Determine signal direction
        signal_direction = self._get_signal_direction(current_index)

        if signal_direction == 0:
            return None

        # Apply additional filters
        if not self._apply_entry_filters(current_index, signal_direction):
            return None

        # Create signal
        if signal_direction > 0:  # Bullish
            return TradingSignal(
                symbol="SYMBOL",
                signal_type=SignalType.BUY,
                timestamp=current_data.name,
                price=current_data['close'],
                confidence=min(signal_strength, 1.0),
                metadata={
                    'signal_strength': signal_strength,
                    'rsi': current_rsi,
                    'atr': current_atr,
                    'trend': 'bullish'
                }
            )
        else:  # Bearish
            return TradingSignal(
                symbol="SYMBOL",
                signal_type=SignalType.SELL,
                timestamp=current_data.name,
                price=current_data['close'],
                confidence=min(signal_strength, 1.0),
                metadata={
                    'signal_strength': signal_strength,
                    'rsi': current_rsi,
                    'atr': current_atr,
                    'trend': 'bearish'
                }
            )
    
    def _check_exit_signals(self, current_index: int) -> Optional[TradingSignal]:
        """Check for exit signals."""
        current_data = self.data.iloc[current_index]
        
        # Implement exit logic (stop loss, take profit, time-based exit)
        # This is a simplified version
        
        if self.current_position and self.stop_loss and self.take_profit:
            current_price = current_data['close']
            
            # Check stop loss
            if ((self.current_position == PositionType.LONG and current_price <= self.stop_loss) or
                (self.current_position == PositionType.SHORT and current_price >= self.stop_loss)):
                
                return TradingSignal(
                    symbol="SYMBOL",
                    signal_type=SignalType.EXIT,
                    timestamp=current_data.name,
                    price=current_price,
                    confidence=1.0,
                    metadata={'exit_reason': 'stop_loss'}
                )
            
            # Check take profit
            if ((self.current_position == PositionType.LONG and current_price >= self.take_profit) or
                (self.current_position == PositionType.SHORT and current_price <= self.take_profit)):
                
                return TradingSignal(
                    symbol="SYMBOL",
                    signal_type=SignalType.EXIT,
                    timestamp=current_data.name,
                    price=current_price,
                    confidence=1.0,
                    metadata={'exit_reason': 'take_profit'}
                )
        
        return None

    def _is_valid_entry_time(self, timestamp) -> bool:
        """Check if current time is valid for entry."""
        if not hasattr(timestamp, 'time'):
            return True

        current_time = timestamp.time()
        entry_start = time.fromisoformat(self.config.entry_start_time)
        stop_trades = time.fromisoformat(self.config.stop_new_trades_time)

        return entry_start <= current_time <= stop_trades

    def _calculate_signal_strength(self, current_index: int) -> float:
        """Calculate overall signal strength based on multiple patterns."""
        total_strength = 0.0

        # Check engulfing pattern
        is_engulfing, engulfing_type = self.patterns.detect_engulfing(self.data, current_index)
        if is_engulfing:
            total_strength += self.config.weight_engulfing

        # Check pin bar pattern
        is_pinbar, pinbar_type = self.patterns.detect_pinbar(self.data, current_index)
        if is_pinbar:
            total_strength += self.config.weight_pinbar

        # Check inside bar pattern
        is_inside, _ = self.patterns.detect_inside_bar(self.data, current_index)
        if is_inside:
            total_strength += self.config.weight_inside_bar

        # Check outside bar pattern
        is_outside, _ = self.patterns.detect_outside_bar(self.data, current_index)
        if is_outside:
            total_strength += self.config.weight_outside_bar

        # Normalize strength
        max_possible_strength = max(
            self.config.weight_engulfing,
            self.config.weight_pinbar,
            self.config.weight_outside_bar
        )

        return total_strength / max_possible_strength if max_possible_strength > 0 else 0.0

    def _get_signal_direction(self, current_index: int) -> int:
        """Get signal direction: 1 for bullish, -1 for bearish, 0 for neutral."""
        bullish_signals = 0
        bearish_signals = 0

        # Check engulfing pattern
        is_engulfing, engulfing_type = self.patterns.detect_engulfing(self.data, current_index)
        if is_engulfing:
            if "bullish" in engulfing_type:
                bullish_signals += 1
            elif "bearish" in engulfing_type:
                bearish_signals += 1

        # Check pin bar pattern
        is_pinbar, pinbar_type = self.patterns.detect_pinbar(self.data, current_index)
        if is_pinbar:
            if "bullish" in pinbar_type:
                bullish_signals += 1
            elif "bearish" in pinbar_type:
                bearish_signals += 1

        # Check trend alignment
        if current_index >= self.config.slow_ma_period:
            fast_ma = self.data['EMA_Fast'].iloc[current_index]
            slow_ma = self.data['EMA_Slow'].iloc[current_index]

            if fast_ma > slow_ma:
                bullish_signals += 1
            elif fast_ma < slow_ma:
                bearish_signals += 1

        # Return direction
        if bullish_signals > bearish_signals:
            return 1
        elif bearish_signals > bullish_signals:
            return -1
        else:
            return 0

    def _apply_entry_filters(self, current_index: int, signal_direction: int) -> bool:
        """Apply additional entry filters."""
        if not self.config.enable_advanced_filters:
            return True

        # RSI filter
        current_rsi = self.data['RSI'].iloc[current_index]

        if signal_direction > 0:  # Bullish signal
            if current_rsi > self.config.rsi_overbought:
                return False
        else:  # Bearish signal
            if current_rsi < self.config.rsi_oversold:
                return False

        # Volatility filter
        if self.config.enable_volatility_filter:
            current_atr = self.data['ATR'].iloc[current_index]
            if current_index >= 20:
                avg_atr = self.data['ATR'].iloc[current_index-20:current_index].mean()
                if current_atr < avg_atr * self.config.volatility_multiplier:
                    return False

        return True

    def on_signal(self, signal: TradingSignal) -> Optional[Order]:
        """Handle trading signals and create orders."""
        current_data = self.data.loc[signal.timestamp]
        
        if signal.signal_type in [SignalType.BUY, SignalType.SELL]:
            # Entry order
            position_type = PositionType.LONG if signal.signal_type == SignalType.BUY else PositionType.SHORT
            
            # Calculate position size, stop loss, and take profit
            atr = current_data['ATR']
            stop_distance = atr * self.config.sl_atr_multiplier
            
            if position_type == PositionType.LONG:
                stop_loss = signal.price - stop_distance
                take_profit = signal.price + (stop_distance * self.config.tp_rr)
            else:
                stop_loss = signal.price + stop_distance
                take_profit = signal.price - (stop_distance * self.config.tp_rr)
            
            # Update strategy state
            self.current_position = position_type
            self.entry_price = signal.price
            self.stop_loss = stop_loss
            self.take_profit = take_profit
            
            # Create order
            order_type = OrderType.BUY if signal.signal_type == SignalType.BUY else OrderType.SELL
            
            return Order(
                symbol=signal.symbol,
                order_type=order_type,
                quantity=1,  # This would be calculated based on position sizing
                price=signal.price,
                timestamp=signal.timestamp,
                metadata=signal.metadata
            )
        
        elif signal.signal_type == SignalType.EXIT:
            # Exit order
            self.current_position = None
            self.entry_price = None
            self.stop_loss = None
            self.take_profit = None
            
            # Create exit order
            order_type = OrderType.SELL if self.current_position == PositionType.LONG else OrderType.BUY
            
            return Order(
                symbol=signal.symbol,
                order_type=order_type,
                quantity=1,
                price=signal.price,
                timestamp=signal.timestamp,
                metadata=signal.metadata
            )
        
        return None
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information."""
        return {
            'name': 'SimplePriceActionStrategy',
            'version': '1.0',
            'description': 'Price action strategy with technical indicators',
            'parameters': {
                'atr_period': self.config.atr_period,
                'fast_ma_period': self.config.fast_ma_period,
                'slow_ma_period': self.config.slow_ma_period,
                'rsi_period': self.config.rsi_period,
                'sl_atr_multiplier': self.config.sl_atr_multiplier,
                'tp_rr': self.config.tp_rr
            }
        }

"""
Backtesting service for managing strategy backtests.
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import pandas as pd

from app.core.logging import get_logger
from app.services.data_service import DataService
from app.services.backtesting import BacktestEngine, BaseStrategy
from app.services.backtesting.sample_strategies import (
    MovingAverageCrossoverStrategy,
    RSIStrategy,
    BollingerBandsStrategy
)
from app.services.backtesting.hybrid_engine import HybridBacktestEngine
from app.database.models import BacktestResult, Strategy
from app.database.repositories.symbol_repository import SymbolRepository

logger = get_logger(__name__)


class BacktestService:
    """Service for managing backtesting operations."""
    
    # Available strategy classes
    STRATEGY_CLASSES = {
        'ma_crossover': MovingAverageCrossoverStrategy,
        'rsi_strategy': RSIStrategy,
        'bollinger_bands': BollingerBandsStrategy
    }
    
    def __init__(self, db: Session):
        """
        Initialize backtest service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.data_service = DataService(db)
        self.symbol_repo = SymbolRepository(db)
    
    def get_available_strategies(self) -> List[Dict[str, Any]]:
        """Get list of available strategies."""
        strategies = []
        
        for strategy_key, strategy_class in self.STRATEGY_CLASSES.items():
            # Create temporary instance to get default parameters
            temp_strategy = strategy_class()
            
            strategies.append({
                'key': strategy_key,
                'name': temp_strategy.name,
                'class_name': strategy_class.__name__,
                'description': strategy_class.__doc__ or "No description available",
                'default_parameters': temp_strategy.parameters
            })
        
        return strategies
    
    def create_strategy(self, strategy_type: str, parameters: Dict[str, Any] = None) -> Optional[BaseStrategy]:
        """
        Create a strategy instance.
        
        Args:
            strategy_type: Strategy type key
            parameters: Strategy parameters
            
        Returns:
            Strategy instance if successful, None otherwise
        """
        try:
            if strategy_type not in self.STRATEGY_CLASSES:
                logger.error(f"Unknown strategy type: {strategy_type}")
                return None
            
            strategy_class = self.STRATEGY_CLASSES[strategy_type]
            
            if parameters:
                # Create strategy with custom parameters
                strategy = strategy_class(**parameters)
            else:
                # Create strategy with default parameters
                strategy = strategy_class()
            
            logger.info(f"Created strategy: {strategy.name}")
            return strategy
            
        except Exception as e:
            logger.error(f"Error creating strategy {strategy_type}: {e}")
            return None
    
    def run_backtest(
        self,
        strategy_type: str,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1d",
        initial_cash: float = 100000.0,
        commission_rate: float = 0.001,
        strategy_parameters: Dict[str, Any] = None,
        position_size_method: str = "fixed_amount",
        position_size_value: float = 10000.0
    ) -> Optional[Dict[str, Any]]:
        """
        Run a backtest.
        
        Args:
            strategy_type: Strategy type
            symbol: Symbol to backtest
            start_date: Backtest start date
            end_date: Backtest end date
            timeframe: Data timeframe
            initial_cash: Initial capital
            commission_rate: Commission rate
            strategy_parameters: Strategy parameters
            position_size_method: Position sizing method
            position_size_value: Position size value
            
        Returns:
            Backtest results if successful, None otherwise
        """
        try:
            logger.info(f"Starting backtest: {strategy_type} on {symbol} from {start_date} to {end_date}")
            
            # Create strategy
            strategy = self.create_strategy(strategy_type, strategy_parameters)
            if not strategy:
                return None
            
            # Get market data
            data = self._get_market_data(symbol, start_date, end_date, timeframe)
            if data is None or len(data) == 0:
                logger.error(f"No market data available for {symbol}")
                return None
            
            # Create backtest engine
            engine = BacktestEngine(
                initial_cash=initial_cash,
                commission_rate=commission_rate,
                position_size_method=position_size_method,
                position_size_value=position_size_value
            )
            
            # Set strategy and data
            engine.set_strategy(strategy)
            engine.set_data(data)
            
            # Run backtest
            results = engine.run_backtest(start_date, end_date, symbol)
            
            # Add additional analysis
            results['equity_curve'] = engine.get_equity_curve().to_dict('records')
            results['trade_analysis'] = engine.get_trade_analysis()
            
            logger.info(f"Backtest completed successfully for {symbol}")
            return results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return None

    def run_hybrid_backtest(
        self,
        symbol: str = "NIFTY50",
        timeframe: int = 30,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        initial_capital: float = 30000,
        margin: float = 0.1,
        commission: float = 0.0,
        strategy_config: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Run a hybrid backtest using the Python backtesting library with custom metrics.

        Args:
            symbol: Symbol to backtest
            timeframe: Timeframe in minutes
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)
            initial_capital: Initial capital
            margin: Margin requirement
            commission: Commission rate
            strategy_config: Strategy configuration

        Returns:
            Comprehensive backtest results
        """
        try:
            logger.info(f"Starting hybrid backtest: {symbol} ({timeframe}min)")

            # Create hybrid engine
            hybrid_engine = HybridBacktestEngine(self.db)

            # Run hybrid backtest
            results = hybrid_engine.run_backtest(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                margin=margin,
                commission=commission,
                strategy_config=strategy_config
            )

            # Close engine
            hybrid_engine.close()

            logger.info(f"Hybrid backtest completed successfully for {symbol}")
            return results

        except Exception as e:
            logger.error(f"Error running hybrid backtest: {e}")
            return None
    
    def _get_market_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str
    ) -> Optional[pd.DataFrame]:
        """Get market data for backtesting."""
        try:
            if timeframe == "1m":
                # Get 1-minute data
                data = self.data_service.get_ohlcv_data(
                    symbol=symbol,
                    start_time=start_date,
                    end_time=end_date,
                    as_dataframe=True
                )
            else:
                # Get aggregated data
                data = self.data_service.get_aggregated_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    start_time=start_date,
                    end_time=end_date
                )
                
                # Convert to DataFrame if needed
                if data and not isinstance(data, pd.DataFrame):
                    data = pd.DataFrame(data)
            
            if data is not None and len(data) > 0:
                logger.info(f"Retrieved {len(data)} bars for {symbol}")
                return data
            else:
                logger.warning(f"No data found for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None
    
    def save_backtest_results(
        self,
        strategy_id: int,
        symbol: str,
        results: Dict[str, Any]
    ) -> bool:
        """
        Save backtest results to database.
        
        Args:
            strategy_id: Strategy ID
            symbol: Symbol
            results: Backtest results
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get symbol ID
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                logger.error(f"Symbol not found: {symbol}")
                return False
            
            # Extract key metrics
            performance = results.get('performance_metrics', {})
            portfolio_summary = results.get('portfolio_summary', {})
            
            # Create backtest result record
            backtest_result = BacktestResult(
                strategy_id=strategy_id,
                symbol_id=symbol_obj.id,
                start_date=datetime.fromisoformat(results['data_info']['start_date']),
                end_date=datetime.fromisoformat(results['data_info']['end_date']),
                initial_capital=results['backtest_config']['initial_cash'],
                final_capital=portfolio_summary.get('final_value', 0),
                total_trades=portfolio_summary.get('total_trades', 0),
                winning_trades=len([t for t in results.get('trades', []) if t.get('pnl', 0) > 0]),
                losing_trades=len([t for t in results.get('trades', []) if t.get('pnl', 0) <= 0]),
                win_rate=portfolio_summary.get('win_rate', 0),
                profit_factor=portfolio_summary.get('profit_factor', 0),
                max_drawdown=performance.get('max_drawdown', 0),
                roi=performance.get('total_return', 0),
                risk_reward_ratio=self._calculate_risk_reward_ratio(results.get('trades', [])),
                extra_data=str(results)  # Store full results as JSON string
            )
            
            self.db.add(backtest_result)
            self.db.commit()
            
            logger.info(f"Saved backtest results for strategy {strategy_id} on {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving backtest results: {e}")
            self.db.rollback()
            return False
    
    def _calculate_risk_reward_ratio(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate risk-reward ratio from trades."""
        try:
            if not trades:
                return 0.0
            
            winning_trades = [t for t in trades if t.get('pnl', 0) > 0]
            losing_trades = [t for t in trades if t.get('pnl', 0) <= 0]
            
            if not winning_trades or not losing_trades:
                return 0.0
            
            avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades)
            avg_loss = abs(sum(t['pnl'] for t in losing_trades) / len(losing_trades))
            
            return avg_win / avg_loss if avg_loss > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating risk-reward ratio: {e}")
            return 0.0
    
    def get_backtest_history(
        self,
        strategy_id: Optional[int] = None,
        symbol: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get backtest history.
        
        Args:
            strategy_id: Filter by strategy ID
            symbol: Filter by symbol
            limit: Maximum number of results
            
        Returns:
            List of backtest results
        """
        try:
            query = self.db.query(BacktestResult)
            
            if strategy_id:
                query = query.filter(BacktestResult.strategy_id == strategy_id)
            
            if symbol:
                symbol_obj = self.symbol_repo.get_by_symbol(symbol)
                if symbol_obj:
                    query = query.filter(BacktestResult.symbol_id == symbol_obj.id)
            
            results = query.order_by(BacktestResult.created_at.desc()).limit(limit).all()
            
            history = []
            for result in results:
                history.append({
                    'id': result.id,
                    'strategy_id': result.strategy_id,
                    'symbol': result.symbol_ref.symbol if result.symbol_ref else 'Unknown',
                    'start_date': result.start_date.isoformat(),
                    'end_date': result.end_date.isoformat(),
                    'total_return': result.roi,
                    'total_trades': result.total_trades,
                    'win_rate': result.win_rate,
                    'max_drawdown': result.max_drawdown,
                    'profit_factor': result.profit_factor,
                    'created_at': result.created_at.isoformat()
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting backtest history: {e}")
            return []
    
    def compare_strategies(
        self,
        strategy_configs: List[Dict[str, Any]],
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1d"
    ) -> Dict[str, Any]:
        """
        Compare multiple strategies on the same data.
        
        Args:
            strategy_configs: List of strategy configurations
            symbol: Symbol to test
            start_date: Start date
            end_date: End date
            timeframe: Data timeframe
            
        Returns:
            Comparison results
        """
        try:
            comparison_results = {
                'symbol': symbol,
                'period': f"{start_date.date()} to {end_date.date()}",
                'timeframe': timeframe,
                'strategies': [],
                'summary': {}
            }
            
            for config in strategy_configs:
                result = self.run_backtest(
                    strategy_type=config['strategy_type'],
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    timeframe=timeframe,
                    initial_cash=config.get('initial_cash', 100000),
                    strategy_parameters=config.get('parameters', {})
                )
                
                if result:
                    comparison_results['strategies'].append({
                        'strategy_name': result['strategy_name'],
                        'total_return': result['performance_metrics'].get('total_return', 0),
                        'max_drawdown': result['performance_metrics'].get('max_drawdown', 0),
                        'sharpe_ratio': result['performance_metrics'].get('sharpe_ratio', 0),
                        'total_trades': result['portfolio_summary'].get('total_trades', 0),
                        'win_rate': result['portfolio_summary'].get('win_rate', 0)
                    })
            
            # Calculate summary statistics
            if comparison_results['strategies']:
                returns = [s['total_return'] for s in comparison_results['strategies']]
                comparison_results['summary'] = {
                    'best_strategy': max(comparison_results['strategies'], key=lambda x: x['total_return']),
                    'worst_strategy': min(comparison_results['strategies'], key=lambda x: x['total_return']),
                    'avg_return': sum(returns) / len(returns),
                    'strategies_tested': len(comparison_results['strategies'])
                }
            
            return comparison_results
            
        except Exception as e:
            logger.error(f"Error comparing strategies: {e}")
            return {'error': str(e)}

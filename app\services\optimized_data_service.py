#!/usr/bin/env python3
"""
Optimized data service for the new TimescaleDB schema.
Works with the optimized composite primary key schema without foreign key dependencies.
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import pandas as pd
from sqlalchemy import text, and_, or_
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.core.logging import get_logger
from app.database.models import Symbol, StockOHLCV, StockOHLCV15Min, StockOHLCV1Hour, StockOHLCV1Day

logger = get_logger(__name__)


class OptimizedDataService:
    """Optimized data service for the new TimescaleDB schema."""
    
    def __init__(self, db: Session):
        """Initialize the optimized data service."""
        self.db = db
    
    def create_symbol(self, symbol_data: Dict[str, Any]) -> Symbol:
        """Create a new symbol."""
        try:
            symbol = Symbol(**symbol_data)
            self.db.add(symbol)
            self.db.commit()
            self.db.refresh(symbol)
            logger.info(f"✅ Created symbol: {symbol.symbol}")
            return symbol
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Failed to create symbol {symbol_data.get('symbol')}: {e}")
            raise
    
    def get_symbol_by_name(self, symbol: str) -> Optional[Symbol]:
        """Get symbol by name."""
        try:
            return self.db.query(Symbol).filter(Symbol.symbol == symbol).first()
        except Exception as e:
            logger.error(f"❌ Failed to get symbol {symbol}: {e}")
            return None
    
    def store_ohlcv_data(
        self,
        symbol: str,
        ohlcv_data: List[Dict[str, Any]],
        exchange: str = "NSE",
        interval: str = "1m",
        upsert: bool = True
    ) -> bool:
        """
        Store OHLCV data using the optimized schema.
        
        Args:
            symbol: Symbol name
            ohlcv_data: List of OHLCV data dictionaries
            exchange: Exchange name (default: NSE)
            interval: Data interval (default: 1m)
            upsert: Use upsert to handle conflicts
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not ohlcv_data:
                return True
            
            # Prepare data for bulk insert with deduplication
            records = []
            seen_timestamps = set()

            for data in ohlcv_data:
                # Convert timestamp to datetime if it's not already
                timestamp = data['timestamp']
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

                # Ensure timestamp is timezone-naive (local time)
                if hasattr(timestamp, 'tzinfo') and timestamp.tzinfo is not None:
                    timestamp = timestamp.replace(tzinfo=None)

                # Skip duplicate timestamps within the same batch
                timestamp_key = (symbol, interval, timestamp)
                if timestamp_key in seen_timestamps:
                    logger.warning(f"Skipping duplicate timestamp {timestamp} for {symbol}")
                    continue
                seen_timestamps.add(timestamp_key)

                record = {
                    'symbol': symbol,
                    'exchange': exchange,
                    'interval': interval,
                    'datetime': timestamp,
                    'open': round(float(data['open']), 2),
                    'high': round(float(data['high']), 2),
                    'low': round(float(data['low']), 2),
                    'close': round(float(data['close']), 2),
                    'volume': int(data['volume'])
                }
                records.append(record)
            
            if upsert:
                # Use PostgreSQL UPSERT (ON CONFLICT DO UPDATE)
                self._bulk_upsert_ohlcv(records)
            else:
                # Use bulk insert
                self._bulk_insert_ohlcv(records)
            
            logger.info(f"✅ Stored {len(records)} OHLCV records for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to store OHLCV data for {symbol}: {e}")
            self.db.rollback()
            return False
    
    def _bulk_upsert_ohlcv(self, records: List[Dict[str, Any]]) -> None:
        """Bulk upsert OHLCV records using raw SQL for performance."""
        if not records:
            return
        
        # Build the VALUES clause
        values_list = []
        for record in records:
            # Ensure datetime is timezone-naive for storage
            dt = record['datetime']
            if hasattr(dt, 'tzinfo') and dt.tzinfo is not None:
                dt = dt.replace(tzinfo=None)

            values_list.append(
                f"('{record['symbol']}', '{record['exchange']}', '{record['interval']}', "
                f"'{dt.isoformat()}', {record['open']:.2f}, {record['high']:.2f}, "
                f"{record['low']:.2f}, {record['close']:.2f}, {record['volume']}, NOW())"
            )
        
        values_clause = ',\n'.join(values_list)
        
        upsert_sql = f"""
            INSERT INTO stock_ohlcv (symbol, exchange, interval, datetime, open, high, low, close, volume, created_at)
            VALUES {values_clause}
            ON CONFLICT (symbol, interval, datetime) 
            DO UPDATE SET
                open = EXCLUDED.open,
                high = EXCLUDED.high,
                low = EXCLUDED.low,
                close = EXCLUDED.close,
                volume = EXCLUDED.volume,
                created_at = EXCLUDED.created_at;
        """
        
        self.db.execute(text(upsert_sql))
        self.db.commit()
    
    def _bulk_insert_ohlcv(self, records: List[Dict[str, Any]]) -> None:
        """Bulk insert OHLCV records."""
        if not records:
            return
        
        # Use SQLAlchemy bulk_insert_mappings for performance
        self.db.bulk_insert_mappings(StockOHLCV, records)
        self.db.commit()
    
    def get_ohlcv_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        interval: str = "1m",
        exchange: str = "NSE",
        as_dataframe: bool = False
    ) -> Optional[Any]:
        """
        Get OHLCV data for a symbol.
        
        Args:
            symbol: Symbol name
            start_time: Start timestamp
            end_time: End timestamp
            interval: Data interval
            exchange: Exchange name
            as_dataframe: Return as pandas DataFrame
            
        Returns:
            OHLCV data as list or DataFrame
        """
        try:
            query = text("""
                SELECT datetime, open, high, low, close, volume
                FROM stock_ohlcv
                WHERE symbol = :symbol
                    AND exchange = :exchange
                    AND interval = :interval
                    AND datetime >= :start_time
                    AND datetime <= :end_time
                ORDER BY datetime
            """)
            
            result = self.db.execute(query, {
                'symbol': symbol,
                'exchange': exchange,
                'interval': interval,
                'start_time': start_time,
                'end_time': end_time
            })
            
            if as_dataframe:
                df = pd.DataFrame(result.fetchall(), columns=[
                    'datetime', 'open', 'high', 'low', 'close', 'volume'
                ])
                if not df.empty:
                    df.set_index('datetime', inplace=True)
                return df
            else:
                return [dict(row._mapping) for row in result]
                
        except Exception as e:
            logger.error(f"❌ Failed to get OHLCV data for {symbol}: {e}")
            return None
    
    def get_latest_ohlcv(
        self,
        symbol: str,
        count: int = 10,
        interval: str = "1m",
        exchange: str = "NSE"
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Get latest OHLCV data for a symbol.
        
        Args:
            symbol: Symbol name
            count: Number of latest records
            interval: Data interval
            exchange: Exchange name
            
        Returns:
            Latest OHLCV records
        """
        try:
            query = text("""
                SELECT datetime, open, high, low, close, volume
                FROM stock_ohlcv
                WHERE symbol = :symbol
                    AND exchange = :exchange
                    AND interval = :interval
                ORDER BY datetime DESC
                LIMIT :count
            """)
            
            result = self.db.execute(query, {
                'symbol': symbol,
                'exchange': exchange,
                'interval': interval,
                'count': count
            })
            
            # Convert to list of dictionaries with proper datetime objects
            records = []
            for row in result:
                record = dict(row._mapping)
                # Create a simple object with timestamp attribute for compatibility
                class OHLCVRecord:
                    def __init__(self, data):
                        self.timestamp = data['datetime']
                        self.open = data['open']
                        self.high = data['high']
                        self.low = data['low']
                        self.close = data['close']
                        self.volume = data['volume']
                
                records.append(OHLCVRecord(record))
            
            return records
            
        except Exception as e:
            logger.error(f"❌ Failed to get latest OHLCV for {symbol}: {e}")
            return None
    
    def get_data_statistics(self, symbol: str, interval: str = "1m", exchange: str = "NSE") -> Dict[str, Any]:
        """
        Get data statistics for a symbol.
        
        Args:
            symbol: Symbol name
            interval: Data interval
            exchange: Exchange name
            
        Returns:
            Statistics dictionary
        """
        try:
            query = text("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(datetime) as first_timestamp,
                    MAX(datetime) as last_timestamp,
                    MIN(low) as min_price,
                    MAX(high) as max_price,
                    AVG(close) as avg_price,
                    SUM(volume) as total_volume
                FROM stock_ohlcv
                WHERE symbol = :symbol
                    AND exchange = :exchange
                    AND interval = :interval
            """)
            
            result = self.db.execute(query, {
                'symbol': symbol,
                'exchange': exchange,
                'interval': interval
            }).fetchone()
            
            if result:
                return {
                    'symbol': symbol,
                    'total_records': result.total_records,
                    'data_range': {
                        'start': result.first_timestamp,
                        'end': result.last_timestamp
                    },
                    'price_range': {
                        'min': float(result.min_price) if result.min_price else None,
                        'max': float(result.max_price) if result.max_price else None,
                        'avg': float(result.avg_price) if result.avg_price else None
                    },
                    'total_volume': int(result.total_volume) if result.total_volume else 0,
                    'days_of_data': (result.last_timestamp - result.first_timestamp).days if result.first_timestamp and result.last_timestamp else 0
                }
            else:
                return {
                    'symbol': symbol,
                    'total_records': 0,
                    'data_range': {'start': None, 'end': None},
                    'price_range': {'min': None, 'max': None, 'avg': None},
                    'total_volume': 0,
                    'days_of_data': 0
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get data statistics for {symbol}: {e}")
            return {}
    
    def get_symbols_with_data(self, exchange: str = "NSE", interval: str = "1m") -> List[str]:
        """Get list of symbols that have data."""
        try:
            query = text("""
                SELECT DISTINCT symbol
                FROM stock_ohlcv
                WHERE exchange = :exchange
                    AND interval = :interval
                ORDER BY symbol
            """)
            
            result = self.db.execute(query, {
                'exchange': exchange,
                'interval': interval
            })
            
            return [row.symbol for row in result]
            
        except Exception as e:
            logger.error(f"❌ Failed to get symbols with data: {e}")
            return []

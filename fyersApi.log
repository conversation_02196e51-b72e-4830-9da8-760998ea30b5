{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 04:57:37,048+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 04:57:37,052+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 651, in __auth_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 04:57:37,421+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 04:57:37,422+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 813, in __lite_full_mode_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-13 04:57:37,792+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 04:57:37,792+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 21:38:20,532+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 21:38:20,536+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 651, in __auth_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-13 21:38:20,767+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 21:38:20,768+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 813, in __lite_full_mode_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-13 21:38:21,794+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-13 21:38:21,795+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-14 15:57:26,665+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 15:57:26,667+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 651, in __auth_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-14 15:57:28,253+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 15:57:28,255+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-14 15:57:28,270+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 15:57:28,271+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 813, in __lite_full_mode_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-14 16:19:27,233+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 428, in setSock\n    dispatcher.read(self.sock.sock, read, check)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 99, in read\n    if not read_callback():\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 442, in read\n    raise e\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 437, in read\n    op_code, frame = self.sock.recv_data_frame(True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 406, in recv_data_frame\n    frame = self.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 445, in recv_frame\n    return self.frame_buffer.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 338, in recv_frame\n    self.recv_header()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 294, in recv_header\n    header = self.recv_strict(2)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 373, in recv_strict\n    bytes_ = self.recv(min(16384, shortage))\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 529, in _recv\n    return recv(self.sock, bufsize)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_socket.py\", line 122, in recv\n    raise WebSocketConnectionClosedException(\nwebsocket._exceptions.WebSocketConnectionClosedException: Connection to remote host was lost.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 16:19:27,247+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 428, in setSock\n    dispatcher.read(self.sock.sock, read, check)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 99, in read\n    if not read_callback():\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 442, in read\n    raise e\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 437, in read\n    op_code, frame = self.sock.recv_data_frame(True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 406, in recv_data_frame\n    frame = self.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 445, in recv_frame\n    return self.frame_buffer.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 338, in recv_frame\n    self.recv_header()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 294, in recv_header\n    header = self.recv_strict(2)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 373, in recv_strict\n    bytes_ = self.recv(min(16384, shortage))\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 529, in _recv\n    return recv(self.sock, bufsize)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_socket.py\", line 122, in recv\n    raise WebSocketConnectionClosedException(\nwebsocket._exceptions.WebSocketConnectionClosedException: Connection to remote host was lost.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-14 16:19:31,148+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 16:19:31,148+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 651, in __auth_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","timestamp":"2025-07-14 16:19:32,525+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 16:19:32,525+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1418, in On_message\n    self.OnMessage(message)\nTypeError: FyersWebSocketClient._on_message() missing 1 required positional argument: 'message'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 813, in __lite_full_mode_resp\n    self.On_message(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1432, in On_message\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-14 16:19:32,621+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 155, in _get_addrinfo_list\n    addrinfo_list = socket.getaddrinfo(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py\", line 955, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 412, in setSock\n    self.sock.connect(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 249, in connect\n    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 122, in connect\n    addrinfo_list, need_tunnel, auth = _get_addrinfo_list(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 167, in _get_addrinfo_list\n    raise WebSocketAddressException(e)\nwebsocket._exceptions.WebSocketAddressException: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 16:19:32,630+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 155, in _get_addrinfo_list\n    addrinfo_list = socket.getaddrinfo(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py\", line 955, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 412, in setSock\n    self.sock.connect(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 249, in connect\n    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 122, in connect\n    addrinfo_list, need_tunnel, auth = _get_addrinfo_list(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 167, in _get_addrinfo_list\n    raise WebSocketAddressException(e)\nwebsocket._exceptions.WebSocketAddressException: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-14 17:20:24,559+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 428, in setSock\n    dispatcher.read(self.sock.sock, read, check)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 99, in read\n    if not read_callback():\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 442, in read\n    raise e\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 437, in read\n    op_code, frame = self.sock.recv_data_frame(True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 406, in recv_data_frame\n    frame = self.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 445, in recv_frame\n    return self.frame_buffer.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 338, in recv_frame\n    self.recv_header()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 294, in recv_header\n    header = self.recv_strict(2)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 373, in recv_strict\n    bytes_ = self.recv(min(16384, shortage))\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 529, in _recv\n    return recv(self.sock, bufsize)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_socket.py\", line 122, in recv\n    raise WebSocketConnectionClosedException(\nwebsocket._exceptions.WebSocketConnectionClosedException: Connection to remote host was lost.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 17:20:24,582+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 428, in setSock\n    dispatcher.read(self.sock.sock, read, check)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 99, in read\n    if not read_callback():\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 442, in read\n    raise e\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 437, in read\n    op_code, frame = self.sock.recv_data_frame(True)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 406, in recv_data_frame\n    frame = self.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 445, in recv_frame\n    return self.frame_buffer.recv_frame()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 338, in recv_frame\n    self.recv_header()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 294, in recv_header\n    header = self.recv_strict(2)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_abnf.py\", line 373, in recv_strict\n    bytes_ = self.recv(min(16384, shortage))\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 529, in _recv\n    return recv(self.sock, bufsize)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_socket.py\", line 122, in recv\n    raise WebSocketConnectionClosedException(\nwebsocket._exceptions.WebSocketConnectionClosedException: Connection to remote host was lost.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-14 17:20:29,565+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 155, in _get_addrinfo_list\n    addrinfo_list = socket.getaddrinfo(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py\", line 955, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 412, in setSock\n    self.sock.connect(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 249, in connect\n    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 122, in connect\n    addrinfo_list, need_tunnel, auth = _get_addrinfo_list(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 167, in _get_addrinfo_list\n    raise WebSocketAddressException(e)\nwebsocket._exceptions.WebSocketAddressException: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 17:20:29,583+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 155, in _get_addrinfo_list\n    addrinfo_list = socket.getaddrinfo(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py\", line 955, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 412, in setSock\n    self.sock.connect(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 249, in connect\n    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 122, in connect\n    addrinfo_list, need_tunnel, auth = _get_addrinfo_list(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 167, in _get_addrinfo_list\n    raise WebSocketAddressException(e)\nwebsocket._exceptions.WebSocketAddressException: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","timestamp":"2025-07-14 17:20:34,577+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 155, in _get_addrinfo_list\n    addrinfo_list = socket.getaddrinfo(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py\", line 955, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 412, in setSock\n    self.sock.connect(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 249, in connect\n    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 122, in connect\n    addrinfo_list, need_tunnel, auth = _get_addrinfo_list(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 167, in _get_addrinfo_list\n    raise WebSocketAddressException(e)\nwebsocket._exceptions.WebSocketAddressException: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'","exception_name":"TypeError"}
{"level":"ERROR","location":"[exception:108] fyers_logger","message":"FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","timestamp":"2025-07-14 17:20:34,580+0530","service":"FyersDataSocket","exception":"Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 155, in _get_addrinfo_list\n    addrinfo_list = socket.getaddrinfo(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py\", line 955, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 412, in setSock\n    self.sock.connect(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_core.py\", line 249, in connect\n    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 122, in connect\n    addrinfo_list, need_tunnel, auth = _get_addrinfo_list(\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_http.py\", line 167, in _get_addrinfo_list\n    raise WebSocketAddressException(e)\nwebsocket._exceptions.WebSocketAddressException: [Errno 11001] getaddrinfo failed\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 547, in _callback\n    callback(self, *args)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 501, in run_forever\n    setSock()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 430, in setSock\n    handleDisconnect(e, reconnecting)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 481, in handleDisconnect\n    self._callback(self.on_error, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\websocket\\_app.py\", line 552, in _callback\n    self.on_error(self, e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1643, in <lambda>\n    on_error=lambda ws, msg: self.On_error(msg),\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1501, in on_open\n    self.OnOpen()\nTypeError: FyersWebSocketClient._on_connect() missing 1 required positional argument: 'ws'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1519, in connect\n    self.on_open()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1504, in on_open\n    self.On_error(e)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\fyers_apiv3\\FyersWebsocket\\data_ws.py\", line 1485, in On_error\n    self.OnError(message)\nTypeError: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'","exception_name":"TypeError"}

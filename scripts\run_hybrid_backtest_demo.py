#!/usr/bin/env python3
"""
Comprehensive demonstration of the Hybrid Backtesting Framework.
This script showcases the complete integration and validates all functionality.
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.backtest_service import BacktestService
from app.services.backtesting.hybrid_engine import HybridBacktestEngine

logger = get_logger(__name__)


def run_comprehensive_demo():
    """Run comprehensive demonstration of hybrid backtesting framework."""
    
    logger.info("🚀 Starting Comprehensive Hybrid Backtesting Framework Demo")
    logger.info("="*80)
    
    # Test configurations
    test_configs = [
        {
            'name': 'Short-term Test (7 days)',
            'symbol': 'NIFTY50',
            'timeframe': 30,
            'start_date': '2025-07-07',
            'end_date': '2025-07-14',
            'description': 'Quick validation test with recent data'
        },
        {
            'name': 'Medium-term Test (1 month)',
            'symbol': 'NIFTY50', 
            'timeframe': 30,
            'start_date': '2025-06-14',
            'end_date': '2025-07-14',
            'description': 'Medium-term validation with 1 month of data'
        },
        {
            'name': 'Full Historical Test',
            'symbol': 'NIFTY50',
            'timeframe': 30,
            'start_date': None,  # Use full dataset
            'end_date': None,
            'description': 'Complete historical backtest (2017-2025)'
        }
    ]
    
    results_summary = []
    
    for i, config in enumerate(test_configs, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"TEST {i}: {config['name']}")
        logger.info(f"{'='*60}")
        logger.info(f"📊 Description: {config['description']}")
        logger.info(f"🎯 Symbol: {config['symbol']}")
        logger.info(f"⏰ Timeframe: {config['timeframe']} minutes")
        
        if config['start_date'] and config['end_date']:
            logger.info(f"📅 Period: {config['start_date']} to {config['end_date']}")
        else:
            logger.info(f"📅 Period: Full historical dataset")
        
        try:
            # Run hybrid backtest
            start_time = time.time()
            
            db = next(get_db())
            hybrid_engine = HybridBacktestEngine(db)
            
            result = hybrid_engine.run_backtest(
                symbol=config['symbol'],
                timeframe=config['timeframe'],
                start_date=config['start_date'],
                end_date=config['end_date'],
                initial_capital=30000,
                margin=0.1,
                commission=0.0
            )
            
            execution_time = time.time() - start_time
            hybrid_engine.close()
            
            # Extract key metrics
            custom_metrics = result['custom_metrics']
            backtest_metrics = result['backtest_library_results']
            
            # Display results
            logger.info(f"\n📈 RESULTS SUMMARY:")
            logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
            logger.info(f"📊 Data Shape: {result['data_shape']}")
            logger.info(f"🎯 Total Trades: {custom_metrics.get('Total Trades', 0)}")
            logger.info(f"✅ Win Rate: {custom_metrics.get('Win Rate (%)', 0):.2f}%")
            logger.info(f"💰 Total Return: {custom_metrics.get('Total Return (%)', 0):.2f}%")
            logger.info(f"📉 Max Drawdown: {custom_metrics.get('Max Drawdown (%)', 0):.2f}%")
            logger.info(f"📊 Sharpe Ratio: {custom_metrics.get('Sharpe Ratio', 0):.2f}")
            logger.info(f"🎲 Profit Factor: {custom_metrics.get('Profit Factor', 0):.2f}")
            
            # Validation status
            validation = result['validation']
            if validation['trades_match'] and validation['win_rate_match']:
                logger.info(f"✅ VALIDATION: PASSED - All metrics match reference")
            else:
                logger.info(f"⚠️  VALIDATION: {len(validation['discrepancies'])} discrepancies found")
                for disc in validation['discrepancies']:
                    logger.info(f"   - {disc}")
            
            # Reports generated
            reports = result['reports']
            logger.info(f"\n📄 REPORTS GENERATED:")
            for report_type, filename in reports.items():
                logger.info(f"   - {report_type.title()}: {filename}")
            
            # Store results for summary
            results_summary.append({
                'test_name': config['name'],
                'symbol': config['symbol'],
                'timeframe': config['timeframe'],
                'execution_time': execution_time,
                'total_trades': custom_metrics.get('Total Trades', 0),
                'win_rate': custom_metrics.get('Win Rate (%)', 0),
                'total_return': custom_metrics.get('Total Return (%)', 0),
                'max_drawdown': custom_metrics.get('Max Drawdown (%)', 0),
                'sharpe_ratio': custom_metrics.get('Sharpe Ratio', 0),
                'validation_passed': validation['trades_match'] and validation['win_rate_match'],
                'reports': reports
            })
            
            logger.info(f"✅ Test {i} completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Test {i} failed: {e}")
            results_summary.append({
                'test_name': config['name'],
                'error': str(e),
                'validation_passed': False
            })
    
    # Generate comprehensive summary
    generate_demo_summary(results_summary)
    
    logger.info(f"\n🎉 Comprehensive Demo Completed!")
    logger.info("="*80)


def generate_demo_summary(results_summary: list):
    """Generate comprehensive demo summary."""
    logger.info(f"\n{'='*80}")
    logger.info(" "*25 + "COMPREHENSIVE DEMO SUMMARY")
    logger.info("="*80)
    
    # Summary table
    logger.info(f"\n📊 RESULTS OVERVIEW:")
    logger.info(f"{'Test Name':<25} | {'Trades':<8} | {'Win Rate':<10} | {'Return':<10} | {'Validation':<12}")
    logger.info("-" * 75)
    
    total_tests = len(results_summary)
    passed_tests = 0
    
    for result in results_summary:
        if 'error' not in result:
            trades = result['total_trades']
            win_rate = f"{result['win_rate']:.1f}%"
            total_return = f"{result['total_return']:.1f}%"
            validation = "✅ PASS" if result['validation_passed'] else "❌ FAIL"
            
            if result['validation_passed']:
                passed_tests += 1
            
            logger.info(f"{result['test_name']:<25} | {trades:<8} | {win_rate:<10} | {total_return:<10} | {validation:<12}")
        else:
            logger.info(f"{result['test_name']:<25} | {'ERROR':<8} | {'N/A':<10} | {'N/A':<10} | {'❌ FAIL':<12}")
    
    # Overall statistics
    logger.info(f"\n📈 OVERALL STATISTICS:")
    logger.info(f"Total Tests Run:           {total_tests}")
    logger.info(f"Tests Passed:              {passed_tests}")
    logger.info(f"Tests Failed:              {total_tests - passed_tests}")
    logger.info(f"Success Rate:              {(passed_tests / total_tests) * 100:.1f}%")
    
    # Performance analysis
    successful_results = [r for r in results_summary if 'error' not in r]
    if successful_results:
        avg_execution_time = sum(r['execution_time'] for r in successful_results) / len(successful_results)
        total_trades = sum(r['total_trades'] for r in successful_results)
        avg_win_rate = sum(r['win_rate'] for r in successful_results) / len(successful_results)
        
        logger.info(f"\n⚡ PERFORMANCE ANALYSIS:")
        logger.info(f"Average Execution Time:    {avg_execution_time:.2f} seconds")
        logger.info(f"Total Trades Analyzed:     {total_trades}")
        logger.info(f"Average Win Rate:          {avg_win_rate:.2f}%")
    
    # Framework validation
    all_validated = all(r.get('validation_passed', False) for r in results_summary if 'error' not in r)
    
    logger.info(f"\n🏆 FRAMEWORK VALIDATION:")
    if all_validated and passed_tests == total_tests:
        logger.info("✅ HYBRID FRAMEWORK FULLY VALIDATED")
        logger.info("   - All metrics match reference implementation")
        logger.info("   - Performance is consistent across different time periods")
        logger.info("   - CSV reports generated successfully")
        logger.info("   - Ready for production use")
    else:
        logger.info("⚠️  FRAMEWORK VALIDATION INCOMPLETE")
        logger.info("   - Some tests failed or metrics don't match")
        logger.info("   - Review failed tests before production use")
    
    # Save summary to file
    summary_filename = f"hybrid_framework_demo_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(summary_filename, 'w') as f:
        json.dump({
            'timestamp': datetime.utcnow().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': (passed_tests / total_tests) * 100,
            'framework_validated': all_validated and passed_tests == total_tests,
            'results': results_summary
        }, f, indent=2, default=str)
    
    logger.info(f"\n📄 Demo summary saved: {summary_filename}")
    logger.info("="*80)


def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run hybrid backtesting framework demo')
    parser.add_argument('--quick', action='store_true', 
                       help='Run only quick validation test')
    parser.add_argument('--full', action='store_true',
                       help='Run full historical test only')
    
    args = parser.parse_args()
    
    if args.quick:
        logger.info("🚀 Running Quick Validation Test")
        # Run only the short-term test
        # Implementation for quick test
    elif args.full:
        logger.info("🚀 Running Full Historical Test")
        # Run only the full historical test
        # Implementation for full test
    else:
        # Run comprehensive demo
        run_comprehensive_demo()


if __name__ == "__main__":
    main()

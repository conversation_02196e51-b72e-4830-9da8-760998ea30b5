"""
Hybrid Backtesting Engine that combines Python backtesting library with custom metrics.
This provides the optimal balance of performance, accuracy, and maintainability.
"""

import os
import sys
import time
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import tempfile

# Import backtesting library
from backtesting import Backtest

from app.core.logging import get_logger
from app.services.optimized_data_service import OptimizedDataService
from app.database.connection import get_db

logger = get_logger(__name__)


class HybridBacktestEngine:
    """
    Hybrid backtesting engine that uses Python backtesting library for execution
    and custom metrics for accurate performance assessment.
    """
    
    def __init__(self, db_session=None):
        """
        Initialize the hybrid backtesting engine.
        
        Args:
            db_session: Database session (optional)
        """
        self.db = db_session or next(get_db())
        self.data_service = OptimizedDataService(self.db)
        self.execution_time = None
        self.memory_usage = None
        
        # Reference project path for strategy imports
        self.reference_path = Path(__file__).parent.parent.parent.parent / "Reference" / "V7_IntradayCleanup_Best_30Min"
        
    def run_backtest(
        self,
        symbol: str = "NIFTY50",
        timeframe: int = 30,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        initial_capital: float = 30000,
        margin: float = 0.1,
        commission: float = 0.0,
        strategy_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Run hybrid backtest combining backtesting library execution with custom metrics.
        
        Args:
            symbol: Symbol to backtest
            timeframe: Timeframe in minutes
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)
            initial_capital: Initial capital
            margin: Margin requirement
            commission: Commission rate
            strategy_config: Strategy configuration
            
        Returns:
            Comprehensive backtest results
        """
        logger.info(f"🚀 Starting Hybrid Backtest for {symbol} ({timeframe}min timeframe)")
        start_time = time.time()
        
        try:
            # Step 1: Load and prepare data
            data = self._load_and_prepare_data(symbol, timeframe, start_date, end_date)
            if data is None or data.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # Step 2: Run backtesting library execution
            backtest_results = self._run_backtesting_library(
                data, initial_capital, margin, commission, strategy_config
            )
            
            # Step 3: Calculate custom metrics
            custom_metrics = self._calculate_custom_metrics(
                backtest_results, initial_capital, margin
            )
            
            # Step 4: Generate reports
            reports = self._generate_reports(backtest_results, custom_metrics, symbol, timeframe)
            
            self.execution_time = time.time() - start_time
            
            # Step 5: Compile comprehensive results
            results = {
                'symbol': symbol,
                'timeframe': timeframe,
                'execution_time': self.execution_time,
                'data_shape': data.shape,
                'backtest_library_results': {
                    'total_return': backtest_results['stats']['Return [%]'],
                    'total_trades': backtest_results['stats']['# Trades'],
                    'win_rate': backtest_results['stats']['Win Rate [%]'],
                    'sharpe_ratio': backtest_results['stats']['Sharpe Ratio'],
                    'max_drawdown': backtest_results['stats']['Max. Drawdown [%]'],
                    'profit_factor': backtest_results['stats']['Profit Factor']
                },
                'custom_metrics': custom_metrics,
                'reports': reports,
                'validation': self._validate_results(backtest_results, custom_metrics),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"✅ Hybrid Backtest completed in {self.execution_time:.2f} seconds")
            logger.info(f"📊 Total trades: {results['backtest_library_results']['total_trades']}")
            logger.info(f"📈 Total return (custom): {custom_metrics.get('Total Return (%)', 0):.2f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Hybrid backtest failed: {e}")
            raise
    
    def _load_and_prepare_data(
        self,
        symbol: str,
        timeframe: int,
        start_date: Optional[str],
        end_date: Optional[str]
    ) -> pd.DataFrame:
        """Load and prepare data for backtesting."""
        logger.info(f"📊 Loading data for {symbol}...")
        
        # Set default date range if not provided
        if not end_date:
            end_date = datetime.now()
        else:
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
            
        if not start_date:
            start_date = datetime(2017, 1, 1)
        else:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
        
        # Fetch data from database
        data = self.data_service.get_ohlcv_data(
            symbol=symbol,
            start_time=start_date,
            end_time=end_date,
            interval="1m",
            exchange="NSE",
            as_dataframe=True
        )
        
        if data is None or data.empty:
            logger.error(f"❌ No data found for {symbol}")
            return None
        
        logger.info(f"✅ Loaded {len(data)} 1-minute records")
        
        # Resample to specified timeframe if needed
        if timeframe > 1:
            logger.info(f"🔄 Resampling to {timeframe}-minute timeframe...")
            rule = f'{timeframe}T'
            
            resampled_data = pd.DataFrame()
            resampled_data['Open'] = data['open'].resample(rule).first()
            resampled_data['High'] = data['high'].resample(rule).max()
            resampled_data['Low'] = data['low'].resample(rule).min()
            resampled_data['Close'] = data['close'].resample(rule).last()
            resampled_data['Volume'] = data['volume'].resample(rule).sum()
            
            # Drop NaN values
            resampled_data.dropna(inplace=True)
            
            logger.info(f"✅ Resampled to {len(resampled_data)} {timeframe}-minute bars")
            return resampled_data
        else:
            # Rename columns for backtesting library
            data = data.rename(columns={
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            })
            return data
    
    def _run_backtesting_library(
        self,
        data: pd.DataFrame,
        initial_capital: float,
        margin: float,
        commission: float,
        strategy_config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Run the backtesting library execution."""
        logger.info("🔧 Running backtesting library execution...")
        
        # Import the reference strategy
        sys.path.insert(0, str(self.reference_path))
        
        try:
            from SimplePriceActionStrategy import SimplePriceActionStrategy
            
            # Create temporary config file if needed
            config_path = None
            if strategy_config:
                config_path = self._create_temp_config(strategy_config)
            
            # Change to reference directory for config access
            original_cwd = os.getcwd()
            os.chdir(str(self.reference_path))
            
            try:
                # Initialize and run backtest
                bt = Backtest(
                    data,
                    SimplePriceActionStrategy,
                    cash=initial_capital,
                    margin=margin,
                    commission=commission,
                    trade_on_close=True,
                    hedging=False,
                    exclusive_orders=True,
                    finalize_trades=True
                )
                
                logger.info("⚡ Executing backtest...")
                stats = bt.run()
                
                # Extract trade data
                trades_data = self._extract_trade_data(bt, stats)
                
                return {
                    'backtest_instance': bt,
                    'stats': stats,
                    'trades_data': trades_data
                }
                
            finally:
                # Restore original working directory
                os.chdir(original_cwd)
                
                # Clean up temporary config
                if config_path and os.path.exists(config_path):
                    os.remove(config_path)
                    
        except Exception as e:
            logger.error(f"❌ Backtesting library execution failed: {e}")
            raise
    
    def _extract_trade_data(self, bt, stats) -> pd.DataFrame:
        """Extract trade data from backtesting results."""
        strategy_instance = bt._strategy
        
        # Check if strategy has internal trade log
        if hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log:
            logger.info(f"📋 Using strategy's internal trade log ({len(strategy_instance.trade_log)} trades)")
            return pd.DataFrame(strategy_instance.trade_log)
        else:
            # Use backtesting framework's trade data
            logger.info("📋 Using backtesting framework's trade data")
            all_trades = stats['_trades']
            
            if all_trades.empty:
                return pd.DataFrame()
            
            trades_list = []
            for idx, trade in all_trades.iterrows():
                entry_time = trade.EntryTime
                exit_time = trade.ExitTime
                entry_price = trade.EntryPrice
                exit_price = trade.ExitPrice
                position_type = "LONG" if trade.Size > 0 else "SHORT"
                duration = exit_time - entry_time
                
                # Calculate profit in points
                if position_type == "LONG":
                    profit_points = exit_price - entry_price
                else:
                    profit_points = entry_price - exit_price
                
                profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0
                
                # Determine exit reason
                exit_time_str = exit_time.strftime('%H:%M')
                if exit_time_str >= "15:15":
                    exit_reason = "Time-based Exit"
                elif profit_points > 0:
                    exit_reason = "Take Profit"
                elif profit_points < 0:
                    exit_reason = "Stop Loss"
                else:
                    exit_reason = "Manual Exit"
                
                trades_list.append({
                    "Index": idx + 1,
                    "Entry DateTime": entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Exit DateTime": exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Entry Price": round(entry_price, 2),
                    "Exit Price": round(exit_price, 2),
                    "Profit Points": round(profit_points, 2),
                    "Profit Percent": round(profit_percent, 2),
                    "Position": position_type,
                    "Trade Duration": str(duration),
                    "Exit Reason": exit_reason
                })
            
            return pd.DataFrame(trades_list)
    
    def _calculate_custom_metrics(
        self,
        backtest_results: Dict[str, Any],
        initial_capital: float,
        margin: float
    ) -> Dict[str, Any]:
        """Calculate custom metrics using reference project's calculation method."""
        logger.info("📊 Calculating custom metrics...")
        
        trades_df = backtest_results['trades_data']
        
        if trades_df.empty:
            logger.warning("⚠️  No trades found for custom metrics calculation")
            return {}
        
        # Import reference metrics calculator
        sys.path.insert(0, str(self.reference_path))
        
        try:
            from SimplePriceActionStrategyMetrics import calculate_metrics
            
            # Calculate metrics using reference implementation
            custom_metrics = calculate_metrics(
                trades_df,
                initial_capital=initial_capital,
                margin=margin
            )
            
            logger.info(f"✅ Custom metrics calculated for {len(trades_df)} trades")
            return custom_metrics
            
        except Exception as e:
            logger.error(f"❌ Custom metrics calculation failed: {e}")
            return {}
    
    def _generate_reports(
        self,
        backtest_results: Dict[str, Any],
        custom_metrics: Dict[str, Any],
        symbol: str,
        timeframe: int
    ) -> Dict[str, str]:
        """Generate CSV reports matching reference project format."""
        logger.info("📄 Generating reports...")

        reports = {}

        try:
            trades_df = backtest_results['trades_data']

            if not trades_df.empty:
                # Filter columns to match reference project format exactly
                required_columns = [
                    "Index", "Entry DateTime", "Exit DateTime", "Entry Price",
                    "Exit Price", "Profit Points", "Profit Percent", "Position",
                    "Trade Duration", "Exit Reason"
                ]

                # Create clean DataFrame with only required columns
                available_columns = [col for col in required_columns if col in trades_df.columns]
                clean_trades_df = trades_df[available_columns].copy()

                # Generate trade log CSV matching reference format
                trade_log_filename = f"hybrid_backtest_{symbol}_{timeframe}min_trades.csv"
                clean_trades_df.to_csv(trade_log_filename, index=False)
                reports['trade_log'] = trade_log_filename
                logger.info(f"📊 Trade log saved: {trade_log_filename}")

                # Generate metrics JSON
                metrics_filename = f"hybrid_backtest_{symbol}_{timeframe}min_metrics.json"
                with open(metrics_filename, 'w') as f:
                    json.dump(custom_metrics, f, indent=2, default=str)
                reports['metrics'] = metrics_filename
                logger.info(f"📈 Metrics saved: {metrics_filename}")

                # Generate summary report matching reference format
                summary_filename = f"hybrid_backtest_{symbol}_{timeframe}min_summary.txt"
                self._generate_summary_report(custom_metrics, summary_filename)
                reports['summary'] = summary_filename
                logger.info(f"📋 Summary report saved: {summary_filename}")

        except Exception as e:
            logger.error(f"❌ Report generation failed: {e}")

        return reports

    def _generate_summary_report(self, custom_metrics: Dict[str, Any], filename: str):
        """Generate a summary report matching reference project format."""
        try:
            with open(filename, 'w') as f:
                f.write("="*80 + "\n")
                f.write(" "*25 + "HYBRID BACKTEST SUMMARY REPORT\n")
                f.write("="*80 + "\n\n")

                # Trade Statistics
                f.write("--- Trade Statistics ---\n")
                f.write(f"Total Trades:                       {custom_metrics.get('Total Trades', 0)}\n")
                f.write(f"Winning Trades:                     {custom_metrics.get('Winning Trades', 0)} ({custom_metrics.get('Win Rate (%)', 0):.2f}%)\n")
                f.write(f"Losing Trades:                      {custom_metrics.get('Losing Trades', 0)} ({100 - custom_metrics.get('Win Rate (%)', 0):.2f}%)\n")
                f.write(f"Profit Factor:                      {custom_metrics.get('Profit Factor', 0):.2f}\n")
                f.write(f"Average Profit per Trade:           {custom_metrics.get('Average Profit per Trade', 0):.2f} points\n")
                f.write(f"Average Winning Trade:              {custom_metrics.get('Average Winning Trade', 0):.2f} points\n")
                f.write(f"Average Losing Trade:               {custom_metrics.get('Average Losing Trade', 0):.2f} points\n\n")

                # Performance Metrics
                f.write("--- Performance Metrics ---\n")
                f.write(f"Initial Capital:                    ${custom_metrics.get('Initial Capital', 0):.2f}\n")
                f.write(f"Final Equity:                       ${custom_metrics.get('Final Equity', 0):.2f}\n")
                f.write(f"Peak Equity:                        ${custom_metrics.get('Peak Equity', 0):.2f}\n")
                f.write(f"Total Return:                       {custom_metrics.get('Total Return (%)', 0):.2f}%\n")
                f.write(f"Annualized Return:                  {custom_metrics.get('Annualized Return (%)', 0):.2f}%\n")
                f.write(f"Max Drawdown:                       {custom_metrics.get('Max Drawdown (%)', 0):.2f}%\n")
                f.write(f"Sharpe Ratio:                       {custom_metrics.get('Sharpe Ratio', 0):.2f}\n")
                f.write(f"Sortino Ratio:                      {custom_metrics.get('Sortino Ratio', 0):.2f}\n\n")

                # Position Analysis
                f.write("--- Position Analysis ---\n")
                f.write(f"Long Trades:                        {custom_metrics.get('Long Trades', 0)}\n")
                f.write(f"Short Trades:                       {custom_metrics.get('Short Trades', 0)}\n")
                f.write(f"Long Win Rate:                      {custom_metrics.get('Long Win Rate (%)', 0):.2f}%\n")
                f.write(f"Short Win Rate:                     {custom_metrics.get('Short Win Rate (%)', 0):.2f}%\n\n")

                f.write("="*80 + "\n")

        except Exception as e:
            logger.error(f"❌ Summary report generation failed: {e}")
    
    def _validate_results(
        self,
        backtest_results: Dict[str, Any],
        custom_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate results between backtesting library and custom metrics."""
        validation = {
            'trades_match': False,
            'win_rate_match': False,
            'discrepancies': []
        }
        
        try:
            stats = backtest_results['stats']
            
            # Check trade count
            lib_trades = stats['# Trades']
            custom_trades = custom_metrics.get('Total Trades', 0)
            validation['trades_match'] = lib_trades == custom_trades
            
            if not validation['trades_match']:
                validation['discrepancies'].append(
                    f"Trade count mismatch: Library={lib_trades}, Custom={custom_trades}"
                )
            
            # Check win rate
            lib_win_rate = stats['Win Rate [%]']
            custom_win_rate = custom_metrics.get('Win Rate (%)', 0)
            win_rate_diff = abs(lib_win_rate - custom_win_rate)
            validation['win_rate_match'] = win_rate_diff < 0.01  # Allow small rounding differences
            
            if not validation['win_rate_match']:
                validation['discrepancies'].append(
                    f"Win rate mismatch: Library={lib_win_rate:.2f}%, Custom={custom_win_rate:.2f}%"
                )
            
            logger.info(f"✅ Validation completed: {len(validation['discrepancies'])} discrepancies found")
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            validation['error'] = str(e)
        
        return validation
    
    def _create_temp_config(self, config: Dict[str, Any]) -> str:
        """Create temporary configuration file."""
        import yaml
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config, f)
            return f.name
    
    def close(self):
        """Close database connection."""
        if self.db:
            self.db.close()

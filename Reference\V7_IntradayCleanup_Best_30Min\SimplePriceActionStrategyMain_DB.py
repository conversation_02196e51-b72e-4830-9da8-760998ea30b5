import pandas as pd
import numpy as np
import os
import yaml
import json
import argparse
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from backtesting import Backtest
from SimplePriceActionStrategy import SimplePriceActionStrategy, load_config
from SimplePriceActionStrategyMetrics import calculate_metrics, print_metrics_comparison, print_detailed_custom_metrics, verify_trade_data_integrity

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.optimized_data_service import OptimizedDataService

logger = get_logger(__name__)

def load_and_process_data_from_db(config):
    """Load and process data from TimescaleDB according to configuration."""
    data_config = config['data']
    
    try:
        logger.info("🔗 Connecting to TimescaleDB...")
        
        # Initialize database connection
        db = next(get_db())
        data_service = OptimizedDataService(db)
        
        # Convert date strings to datetime objects
        start_date = datetime.strptime(data_config['start_date'], "%Y-%m-%d")
        end_date = datetime.strptime(data_config['end_date'], "%Y-%m-%d")
        
        logger.info(f"📊 Fetching NIFTY50 data from {data_config['start_date']} to {data_config['end_date']}")
        
        # Get data from database
        data = data_service.get_ohlcv_data(
            symbol="NIFTY50",
            start_time=start_date,
            end_time=end_date,
            interval="1m",
            exchange="NSE",
            as_dataframe=True
        )
        
        if data is None or data.empty:
            logger.error("❌ No data found in database")
            exit()
        
        logger.info(f"✅ Retrieved {len(data)} records from database")
        
        # Rename columns to match backtesting library expectations
        data = data.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # Ensure proper column order and types
        data = data[['Open', 'High', 'Low', 'Close', 'Volume']]
        
        # Round price columns to 2 decimal places
        price_columns = ['Open', 'High', 'Low', 'Close']
        for col in price_columns:
            data[col] = data[col].round(2)
        
        db.close()
        
    except Exception as e:
        logger.error(f"❌ Error loading data from database: {e}")
        exit()

    print(f"Data loaded and processed from DB. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")

    # Resample data to the specified timeframe if needed
    timeframe = data_config.get('timeframe', 1)  # Default to 1 minute if not specified
    if timeframe > 1:
        print(f"Resampling data from 1-minute to {timeframe}-minute timeframe...")
        
        # Make sure the index is a DatetimeIndex
        if not isinstance(data.index, pd.DatetimeIndex):
            print("Error: Index must be a DatetimeIndex for resampling. Check your data configuration.")
            exit()
        
        # Define the resampling rule
        rule = f'{timeframe}T'  # T stands for minutes in pandas resampling
        
        # Resample OHLCV data
        resampled_data = pd.DataFrame()
        resampled_data['Open'] = data['Open'].resample(rule).first()
        resampled_data['High'] = data['High'].resample(rule).max()
        resampled_data['Low'] = data['Low'].resample(rule).min()
        resampled_data['Close'] = data['Close'].resample(rule).last()
        resampled_data['Volume'] = data['Volume'].resample(rule).sum()
        
        # Drop any NaN values that might have been introduced
        resampled_data.dropna(inplace=True)
        
        print(f"Data resampled to {timeframe}-minute timeframe. New shape: {resampled_data.shape}")
        print(f"Resampled data head:\n{resampled_data.head()}")
        
        return resampled_data
    
    return data

def run_backtest(config, data):
    """Run backtest with configuration."""
    backtest_config = config['backtest']

    # Initialize and run the backtest
    bt = Backtest(
        data,
        SimplePriceActionStrategy,
        cash=backtest_config['cash'],
        margin=backtest_config['margin'],
        commission=backtest_config['commission'],
        trade_on_close=True,
        hedging=False,
        exclusive_orders=True,
        finalize_trades=True
    )

    print("Running backtest...")
    start_time = time.time()
    stats = bt.run()
    execution_time = time.time() - start_time
    
    print(f"\n⏱️  Backtest Execution Time: {execution_time:.2f} seconds")
    print("\n--- Backtest Performance Report ---")
    print(stats)

    return bt, stats, execution_time

def print_detailed_metrics(stats, config):
    """Print detailed performance metrics."""
    if not config['logging']['enable_detailed_metrics']:
        return

    print(f"\n--- Detailed Metrics (Backtesting Library) ---")
    print(f"Total Return (%):                 {stats['Return [%]']:.2f}%")
    if 'Return (Ann.) [%]' in stats:
        print(f"Annualized Return (%):            {stats['Return (Ann.) [%]']:.2f}%")
    else:
        print(f"Annualized Return (%):            N/A (duration < 1 year or not calculated)")
    print(f"Max Drawdown (%):                 {stats['Max. Drawdown [%]']:.2f}%")
    print(f"Sharpe Ratio (annualized):        {stats['Sharpe Ratio']:.2f}")
    print(f"Sortino Ratio (annualized):       {stats['Sortino Ratio']:.2f}")
    print(f"Win Rate (%):                     {stats['Win Rate [%]']:.2f}%")
    print(f"Profit Factor:                    {stats['Profit Factor']:.2f}")
    print(f"Total Number of Trades:           {stats['# Trades']}")

def analyze_strategy_performance(trade_log_path, config):
    """
    Analyze the performance of a trading strategy using the trade log file.
    """
    print("\n" + "="*80)
    print(" "*25 + "STRATEGY PERFORMANCE ANALYSIS")
    print("="*80)
    
    # Load trade data
    if not os.path.exists(trade_log_path):
        print(f"ERROR: Trade log file '{trade_log_path}' not found.")
        return
    
    trades_df = pd.read_csv(trade_log_path)
    print(f"Loaded trade log with {len(trades_df)} trades.")
    
    # Check if the DataFrame is empty
    if len(trades_df) == 0:
        print("No trades found in the trade log. Returning default metrics.")
        return {
            'Total Return (%)': 0.0,
            'Max Drawdown (%)': 0.0,
            'Win Rate (%)': 0.0,
            'Profit Factor': 0.0,
            'Total Trades': 0
        }
    
    print(f"Date range: {trades_df['Entry DateTime'].iloc[0]} to {trades_df['Exit DateTime'].iloc[-1]}")
    
    # Get configuration values
    initial_capital = config['backtest'].get('cash', 10000)
    margin = config['backtest'].get('margin', 1.0)
    print(f"Initial capital: ${initial_capital}")
    print(f"Margin: {margin} (Leverage: {1/margin:.1f}x)")
    
    # Verify trade data integrity
    integrity_verified = verify_trade_data_integrity(trades_df)
    
    if integrity_verified:
        # Calculate custom metrics
        custom_metrics = calculate_metrics(
            trades_df, 
            initial_capital=initial_capital,
            margin=margin
        )
        
        # Print detailed custom metrics
        print_detailed_custom_metrics(custom_metrics)
        
        print("\nAnalysis completed successfully.")
    else:
        print("\nWARNING: Trade data integrity check failed. Metrics may not be reliable.")
    
    return custom_metrics if integrity_verified else None

def export_trade_log(bt, stats, config):
    """Export trade log to CSV."""
    print("\n--- Exporting Trade Log to CSV ---")

    strategy_instance = bt._strategy
    output_config = config['output']

    # Check if strategy has internal trade log
    if hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log:
        print(f"Strategy has internal trade log with {len(strategy_instance.trade_log)} entries")

        # Create DataFrame from trade log
        trades_df = pd.DataFrame(strategy_instance.trade_log)

        # Reorder columns to match required format
        available_columns = [col for col in output_config['required_trade_columns'] if col in trades_df.columns]
        trades_df = trades_df[available_columns]

        # Export to CSV
        output_file = output_config['trade_log_file']
        trades_df.to_csv(output_file, index=False)
        print(f"Trade log exported to: {output_file} (using strategy's internal trade log)")
        print(f"Total trades exported: {len(trades_df)}")
    else:
        # Use backtesting framework's trade data as fallback
        print("Using backtesting framework's trade data instead")
        all_trades = stats['_trades']
        if not all_trades.empty:
            trades_list = []
            for idx, trade in all_trades.iterrows():
                entry_time = trade.EntryTime
                exit_time = trade.ExitTime
                entry_price = trade.EntryPrice
                exit_price = trade.ExitPrice
                position_type = "LONG" if trade.Size > 0 else "SHORT"
                duration = exit_time - entry_time

                # Calculate profit in points
                if position_type == "LONG":
                    profit_points = exit_price - entry_price
                else:  # SHORT
                    profit_points = entry_price - exit_price

                profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0

                # Determine exit reason based on profit/loss
                exit_time_str = exit_time.strftime('%H:%M')
                if exit_time_str >= "15:15":
                    exit_reason = "Time-based Exit"
                # For profitable trades, it's a take profit
                elif profit_points > 0:
                    exit_reason = "Take Profit"
                # For losing trades, it's a stop loss
                elif profit_points < 0:
                    exit_reason = "Stop Loss"
                else:
                    exit_reason = "Manual Exit"

                trades_list.append({
                    "Index": idx + 1,
                    "Entry DateTime": entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Exit DateTime": exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Entry Price": round(entry_price, 2),
                    "Exit Price": round(exit_price, 2),
                    "Profit Points": round(profit_points, 2),
                    "Profit Percent": round(profit_percent, 2),
                    "Position": position_type,
                    "Trade Duration": str(duration),
                    "Exit Reason": exit_reason
                })

            # Create DataFrame and export to CSV
            trades_df = pd.DataFrame(trades_list)
            output_file = output_config['trade_log_file']
            trades_df.to_csv(output_file, index=False)
            print(f"Trade log exported to: {output_file}")
            print(f"Total trades exported: {len(trades_df)}")
        else:
            print("No trades were executed, so no trade log is available to export.")

def main_db_version(config_path="SimplePriceActionStrategyConfig.yaml", timeframe=30):
    """Main execution function using database data."""
    
    # Load configuration
    config = load_config(config_path)
    
    # Update timeframe in config
    config['data']['timeframe'] = timeframe
    
    # Update output file name to include timeframe and DB suffix
    base_filename = config['output']['trade_log_file']
    filename_parts = base_filename.split('.')
    new_filename = f"{filename_parts[0]}_db_{timeframe}min.{filename_parts[1]}"
    config['output']['trade_log_file'] = new_filename
    
    # Also update the JSON stats filename
    global_stats_filename = f"backtest_stats_db_{timeframe}min.json"
    
    print("\n" + "="*80)
    print(f" "*15 + f"SIMPLE PRICE ACTION STRATEGY (DB VERSION) - {timeframe}min TIMEFRAME")
    print("="*80)

    # Load and process data from database
    data = load_and_process_data_from_db(config)

    # Run backtest
    bt, stats, execution_time = run_backtest(config, data)

    # Print detailed metrics from backtesting library
    print_detailed_metrics(stats, config)

    # Export trade log
    export_trade_log(bt, stats, config)
    
    # Get the trade log file path from config
    trade_log_file = config['output']['trade_log_file']
    
    print("\n" + "="*80)
    print(" "*20 + "CUSTOM METRICS CALCULATION")
    print("="*80)
    
    # Save stats to JSON file with timeframe in the filename
    try:
        # Convert stats to a dictionary (excluding non-serializable objects)
        stats_dict = {k: v for k, v in stats.items() if isinstance(v, (int, float, str, bool, list, dict)) or v is None}
        stats_dict['execution_time_seconds'] = execution_time
        
        # Save to JSON file
        with open(global_stats_filename, 'w') as f:
            json.dump(stats_dict, f, indent=4)
        
        print(f"Backtest statistics saved to '{global_stats_filename}'")
    except Exception as e:
        print(f"Warning: Could not save backtest statistics to JSON: {e}")
    
    # Analyze strategy performance using custom metrics
    print("\nCalculating custom metrics from trade log data...")
    custom_metrics = analyze_strategy_performance(trade_log_file, config)
    
    # Load backtest stats from JSON file for comparison
    try:
        with open(global_stats_filename, 'r') as f:
            backtest_stats = json.load(f)
    except Exception as e:
        print(f"Warning: Could not load backtest statistics from JSON: {e}")
        backtest_stats = None
    
    # Compare metrics if both are available
    if custom_metrics and backtest_stats:
        print("\nComparing backtesting library metrics with custom metrics...")
        from SimplePriceActionStrategyMetrics import print_metrics_comparison
        print_metrics_comparison(custom_metrics, backtest_stats)
    
    print("\n" + "="*80)
    print(f" "*15 + f"DB BACKTEST COMPLETED - {timeframe}min TIMEFRAME")
    print(f" "*20 + f"Execution Time: {execution_time:.2f} seconds")
    print("="*80)
    
    return {
        'execution_time': execution_time,
        'total_trades': stats['# Trades'],
        'total_return': stats['Return [%]'],
        'custom_metrics': custom_metrics,
        'backtest_stats': backtest_stats
    }

if __name__ == '__main__':
    try:
        result = main_db_version()
        print(f"\n🎉 Database version completed successfully!")
        print(f"⏱️  Total execution time: {result['execution_time']:.2f} seconds")
    except Exception as e:
        print(f"❌ Error running database version: {e}")
        import traceback
        traceback.print_exc()

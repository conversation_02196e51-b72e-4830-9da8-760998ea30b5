import pandas as pd
import numpy as np
import yaml
from typing import Tu<PERSON>, List, Dict, Union, Optional

def load_config(config_path="SimplePriceActionStrategyConfig.yaml"):
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}

class PriceActionPatterns:
    
    """
    A class containing methods to detect common candlestick patterns.
    """
    
    def __init__(self, config_path="SimplePriceActionStrategyConfig.yaml"):
        """
        Initialize the PriceActionPatterns class with configuration.
        
        Args:
            config_path: Path to the configuration file
        """
        # Load configuration
        config = load_config(config_path)
        self.config = config.get('strategy', {}).get('price_action_patterns', {})
        
        # Set default values if configuration is missing
        if not self.config:
            self.config = {
                'engulfing': {'threshold': 0.0},
                'pin_bars': {'body_ratio': 0.3, 'tail_ratio': 0.6},
                'doji': {'threshold': 0.05},
                'double_top_bottom': {
                    'lookback': 10, 
                    'threshold': 0.01,
                    'process_interval': 5,
                    'top_confirmation_factor': 0.99,
                    'bottom_confirmation_factor': 1.01
                },
                'volume_confirmation': {'lookback': 20, 'multiplier': 1.5, 'enable': True},
                'momentum_divergence': {'lookback': 10, 'process_interval': 5},
                'breakout_strength': {'lookback': 20, 'threshold': 0.5},
                'consolidation_breakout': {'lookback': 10, 'threshold': 0.005},
                'take_profit': {'num_key_levels': 3}
            }

    """
    A class for detecting various price action patterns and generating trading signals.
    This module can be used standalone or integrated with existing strategies.
    """
    def detect_engulfing(self, open_prices: np.ndarray, close_prices: np.ndarray, high_prices: np.ndarray, 
                   low_prices: np.ndarray, threshold: float = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Detect bullish and bearish engulfing patterns.
        
        Args:
            open_prices: Array of opening prices
            close_prices: Array of closing prices
            high_prices: Array of high prices
            low_prices: Array of low prices
            threshold: Minimum size threshold as percentage of candle range (optional)
            
        Returns:
            Tuple of (bullish_engulfing, bearish_engulfing) boolean arrays
        """
        # Use configuration value if threshold is not provided
        if threshold is None:
            threshold = self.config.get('engulfing', {}).get('threshold', 0.0)
        length = len(close_prices)
        bullish_engulfing = np.zeros(length, dtype=bool)
        bearish_engulfing = np.zeros(length, dtype=bool)
        
        for i in range(1, length):
            prev_body_size = abs(close_prices[i-1] - open_prices[i-1])
            curr_body_size = abs(close_prices[i] - open_prices[i])
            
            # Bullish engulfing
            if (open_prices[i] < close_prices[i] and  # Current candle is bullish
                open_prices[i-1] > close_prices[i-1] and  # Previous candle is bearish
                open_prices[i] <= close_prices[i-1] and  # Current open below or equal to previous close
                close_prices[i] >= open_prices[i-1] and  # Current close above or equal to previous open
                curr_body_size > prev_body_size * (1 + threshold)):  # Current body is larger by threshold
                bullish_engulfing[i] = True
                
            # Bearish engulfing
            if (open_prices[i] > close_prices[i] and  # Current candle is bearish
                open_prices[i-1] < close_prices[i-1] and  # Previous candle is bullish
                open_prices[i] >= close_prices[i-1] and  # Current open above or equal to previous close
                close_prices[i] <= open_prices[i-1] and  # Current close below or equal to previous open
                curr_body_size > prev_body_size * (1 + threshold)):  # Current body is larger by threshold
                bearish_engulfing[i] = True
                
        return bullish_engulfing, bearish_engulfing
    
    def detect_pin_bars(self, open_prices: np.ndarray, close_prices: np.ndarray, high_prices: np.ndarray, 
                      low_prices: np.ndarray, body_ratio: float = None, 
                      tail_ratio: float = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Detect bullish and bearish pin bars (hammers and shooting stars).
        
        Args:
            open_prices: Array of opening prices
            close_prices: Array of closing prices
            high_prices: Array of high prices
            low_prices: Array of low prices
            body_ratio: Maximum ratio of body to total candle size (optional)
            tail_ratio: Minimum ratio of tail to total candle size (optional)
            
        Returns:
            Tuple of (bullish_pins, bearish_pins) boolean arrays
        """
        # Use configuration values if parameters are not provided
        if body_ratio is None:
            body_ratio = self.config.get('pin_bars', {}).get('body_ratio', 0.3)
        if tail_ratio is None:
            tail_ratio = self.config.get('pin_bars', {}).get('tail_ratio', 0.6)
        length = len(close_prices)
        bullish_pins = np.zeros(length, dtype=bool)
        bearish_pins = np.zeros(length, dtype=bool)
        
        for i in range(length):
            candle_range = high_prices[i] - low_prices[i]
            if candle_range == 0:  # Avoid division by zero
                continue
                
            body_size = abs(close_prices[i] - open_prices[i])
            body_to_range = body_size / candle_range
            
            # Calculate upper and lower wicks
            upper_wick = high_prices[i] - max(open_prices[i], close_prices[i])
            lower_wick = min(open_prices[i], close_prices[i]) - low_prices[i]
            
            # Bullish pin bar (hammer)
            if (body_to_range <= body_ratio and 
                lower_wick / candle_range >= tail_ratio and
                upper_wick / candle_range <= (1 - tail_ratio - body_ratio)):
                bullish_pins[i] = True
                
            # Bearish pin bar (shooting star)
            if (body_to_range <= body_ratio and 
                upper_wick / candle_range >= tail_ratio and
                lower_wick / candle_range <= (1 - tail_ratio - body_ratio)):
                bearish_pins[i] = True
                
        return bullish_pins, bearish_pins
    
    def detect_inside_bars(self, high_prices: np.ndarray, low_prices: np.ndarray) -> np.ndarray:
        """
        Detect inside bars (current bar's range is within previous bar's range).
        
        Args:
            high_prices: Array of high prices
            low_prices: Array of low prices
            
        Returns:
            Boolean array indicating inside bars
        """
        length = len(high_prices)
        inside_bars = np.zeros(length, dtype=bool)
        
        for i in range(1, length):
            if (high_prices[i] <= high_prices[i-1] and 
                low_prices[i] >= low_prices[i-1]):
                inside_bars[i] = True
                
        return inside_bars
    
    def detect_outside_bars(self, high_prices: np.ndarray, low_prices: np.ndarray) -> np.ndarray:
        """
        Detect outside bars (current bar's range completely engulfs previous bar's range).
        
        Args:
            high_prices: Array of high prices
            low_prices: Array of low prices
            
        Returns:
            Boolean array indicating outside bars
        """
        length = len(high_prices)
        outside_bars = np.zeros(length, dtype=bool)
        
        for i in range(1, length):
            if (high_prices[i] > high_prices[i-1] and 
                low_prices[i] < low_prices[i-1]):
                outside_bars[i] = True
                
        return outside_bars

    def detect_breakout_strength(self, open_prices: np.ndarray, close_prices: np.ndarray, 
                              high_prices: np.ndarray, low_prices: np.ndarray, 
                              volume: np.ndarray, lookback: int = None, 
                              threshold: float = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Detect strong breakouts with volume confirmation.
        Optimized for performance with vectorized operations where possible.
        
        Args:
            open_prices: Array of opening prices
            close_prices: Array of closing prices
            high_prices: Array of high prices
            low_prices: Array of low prices
            volume: Array of volume data
            lookback: Number of bars to establish range (optional)
            threshold: Minimum body to range ratio for strong candle (optional)
            
        Returns:
            Tuple of (bullish_breakouts, bearish_breakouts) boolean arrays
        """
        # Use configuration values if parameters are not provided
        config = self.config.get('breakout_strength', {})
        if lookback is None:
            lookback = config.get('lookback', 20)
        if threshold is None:
            threshold = config.get('threshold', 0.5)
        length = len(close_prices)
        bullish_breakouts = np.zeros(length, dtype=bool)
        bearish_breakouts = np.zeros(length, dtype=bool)
        
        if length <= lookback:
            return bullish_breakouts, bearish_breakouts
        
        # Calculate rolling highest high and lowest low
        highest_high = np.zeros(length)
        lowest_low = np.zeros(length)
        
        # Pre-calculate bullish and bearish candles once
        is_bullish = close_prices > open_prices
        is_bearish = close_prices < open_prices
        
        # Pre-calculate body sizes and ratios for all candles
        body_sizes = np.abs(close_prices - open_prices)
        candle_ranges = high_prices - low_prices
        
        # Initialize body_ratios with zeros
        body_ratios = np.zeros_like(candle_ranges)
        
        # Calculate body ratios only where candle_ranges > 0 to avoid division by zero
        valid_ranges = candle_ranges > 0
        body_ratios[valid_ranges] = body_sizes[valid_ranges] / candle_ranges[valid_ranges]
        
        # Strong body condition
        strong_body = body_ratios >= threshold
        
        # Calculate highest highs and lowest lows
        for i in range(lookback, length):
            highest_high[i] = np.max(high_prices[i-lookback:i])
            lowest_low[i] = np.min(low_prices[i-lookback:i])
            
            # Bullish breakout - using pre-calculated conditions
            if (close_prices[i] > highest_high[i-1] and  # Close above previous highest high
                is_bullish[i] and                        # Bullish candle
                strong_body[i]):                         # Strong body
                bullish_breakouts[i] = True
                
            # Bearish breakout - using pre-calculated conditions
            if (close_prices[i] < lowest_low[i-1] and    # Close below previous lowest low
                is_bearish[i] and                        # Bearish candle
                strong_body[i]):                         # Strong body
                bearish_breakouts[i] = True
                
        return bullish_breakouts, bearish_breakouts

import pandas as pd
import numpy as np
import os
import yaml
import json
import argparse
import sys
from backtesting import Backtest
from SimplePriceActionStrategy import SimplePriceActionStrategy, load_config
from SimplePriceActionStrategyMetrics import calculate_metrics, print_metrics_comparison, print_detailed_custom_metrics, verify_trade_data_integrity

def load_and_process_data(config):
    """Load and process data according to configuration."""
    data_config = config['data']

    try:
        data = pd.read_csv(
            data_config['file_path'],
            parse_dates=[data_config['date_column']] if data_config['parse_dates'] else None,
            index_col=data_config['index_column'] if data_config['index_column'] else None
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {data_config['file_path']}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    column_map_lower = {col.lower(): col for col in data.columns}

    rename_dict = {}
    # Map standard OHLC names first
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased 'bt_name' (e.g. 'Open') already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()

    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        # If 'volume' (lowercase) exists, rename its original cased version to 'Volume'
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns: # Check if 'Volume' (exact case) is already present
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0

    # Verify final column names
    expected_bt_cols = data_config['required_columns']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")

    # Apply date filtering if configured
    if data_config.get('start_date') and data_config.get('end_date'):
        data = data.loc[data_config['start_date']:data_config['end_date']]
        print(f"Data filtered for testing. New shape: {data.shape}")

    # Resample data to the specified timeframe if needed
    timeframe = data_config.get('timeframe', 1)  # Default to 1 minute if not specified
    if timeframe > 1:
        print(f"Resampling data from 1-minute to {timeframe}-minute timeframe...")
        
        # Make sure the index is a DatetimeIndex
        if not isinstance(data.index, pd.DatetimeIndex):
            print("Error: Index must be a DatetimeIndex for resampling. Check your data configuration.")
            exit()
        
        # Define the resampling rule
        rule = f'{timeframe}T'  # T stands for minutes in pandas resampling
        
        # Resample OHLCV data
        resampled_data = pd.DataFrame()
        resampled_data['Open'] = data['Open'].resample(rule).first()
        resampled_data['High'] = data['High'].resample(rule).max()
        resampled_data['Low'] = data['Low'].resample(rule).min()
        resampled_data['Close'] = data['Close'].resample(rule).last()
        resampled_data['Volume'] = data['Volume'].resample(rule).sum()
        
        # Drop any NaN values that might have been introduced
        resampled_data.dropna(inplace=True)
        
        print(f"Data resampled to {timeframe}-minute timeframe. New shape: {resampled_data.shape}")
        print(f"Resampled data head:\n{resampled_data.head()}")
        
        return resampled_data
    
    return data

def run_backtest(config, data):
    """Run backtest with configuration."""
    backtest_config = config['backtest']

    # Initialize and run the backtest
    bt = Backtest(
        data,
        SimplePriceActionStrategy,
        cash=backtest_config['cash'],
        margin=backtest_config['margin'],
        commission=backtest_config['commission'],
        trade_on_close=True,
        hedging=False,
        exclusive_orders=True,
        finalize_trades=True
    )

    print("Running backtest...")
    stats = bt.run()
    print("\n--- Backtest Performance Report ---")
    print(stats)

    return bt, stats

def print_detailed_metrics(stats, config):
    """Print detailed performance metrics."""
    if not config['logging']['enable_detailed_metrics']:
        return

    print(f"\n--- Detailed Metrics (Backtesting Library) ---")
    print(f"Total Return (%):                 {stats['Return [%]']:.2f}%")
    if 'Return (Ann.) [%]' in stats:
        print(f"Annualized Return (%):            {stats['Return (Ann.) [%]']:.2f}%")
    else:
        print(f"Annualized Return (%):            N/A (duration < 1 year or not calculated)")
    print(f"Max Drawdown (%):                 {stats['Max. Drawdown [%]']:.2f}%")
    print(f"Sharpe Ratio (annualized):        {stats['Sharpe Ratio']:.2f}")
    print(f"Sortino Ratio (annualized):       {stats['Sortino Ratio']:.2f}")
    print(f"Win Rate (%):                     {stats['Win Rate [%]']:.2f}%")
    print(f"Profit Factor:                    {stats['Profit Factor']:.2f}")
    print(f"Total Number of Trades:           {stats['# Trades']}")


def analyze_strategy_performance(trade_log_path, config):
    """
    Analyze the performance of a trading strategy using the trade log file.
    
    Parameters:
    -----------
    trade_log_path : str
        Path to the trade log CSV file
    config : dict
        Strategy configuration dictionary
    """
    print("\n" + "="*80)
    print(" "*25 + "STRATEGY PERFORMANCE ANALYSIS")
    print("="*80)
    
    # Load trade data
    if not os.path.exists(trade_log_path):
        print(f"ERROR: Trade log file '{trade_log_path}' not found.")
        return
    
    trades_df = pd.read_csv(trade_log_path)
    print(f"Loaded trade log with {len(trades_df)} trades.")
    
    # Check if the DataFrame is empty
    if len(trades_df) == 0:
        print("No trades found in the trade log. Returning default metrics.")
        return {
            'Total Return (%)': 0.0,
            'Max Drawdown (%)': 0.0,
            'Win Rate (%)': 0.0,
            'Profit Factor': 0.0,
            'Total Trades': 0
        }
    
    print(f"Date range: {trades_df['Entry DateTime'].iloc[0]} to {trades_df['Exit DateTime'].iloc[-1]}")
    
    # Get configuration values
    initial_capital = config['backtest'].get('cash', 10000)
    margin = config['backtest'].get('margin', 1.0)
    print(f"Initial capital: ${initial_capital}")
    print(f"Margin: {margin} (Leverage: {1/margin:.1f}x)")
    
    # Verify trade data integrity
    integrity_verified = verify_trade_data_integrity(trades_df)
    
    if integrity_verified:
        # Calculate custom metrics
        custom_metrics = calculate_metrics(
            trades_df, 
            initial_capital=initial_capital,
            margin=margin
        )
        
        # Print detailed custom metrics
        print_detailed_custom_metrics(custom_metrics)
        
        print("\nAnalysis completed successfully.")
    else:
        print("\nWARNING: Trade data integrity check failed. Metrics may not be reliable.")
    
    return custom_metrics if integrity_verified else None


def compare_metrics(custom_metrics, backtest_stats):
    """
    Compare metrics from backtesting library with custom metrics.
    
    Parameters:
    -----------
    custom_metrics : dict
        Dictionary of custom calculated metrics
    backtest_stats : dict
        Dictionary containing backtest statistics from the backtesting library
    """
    if custom_metrics is None:
        print("\nCannot compare metrics: Custom metrics calculation failed.")
        return
        
    # Print comparison
    print_metrics_comparison(custom_metrics, backtest_stats)
    
    # Print additional information
    print("\nDetailed Analysis:")
    print(f"1. Total Return: The backtesting library reports {backtest_stats.get('Return [%]', 'N/A')}%, " +
          f"while our calculation shows {custom_metrics.get('Total Return (%)', 'N/A'):.2f}%.")
    
    if abs(custom_metrics.get('Total Return (%)', 0) - backtest_stats.get('Return [%]', 0)) > 100:
        print("   - This large discrepancy suggests the backtesting library may be applying leverage incorrectly.")
    
    print(f"2. Max Drawdown: The backtesting library reports {backtest_stats.get('Max. Drawdown [%]', 'N/A')}%, " +
          f"while our calculation shows {custom_metrics.get('Max Drawdown (%)', 'N/A'):.2f}%.")
    
    print(f"3. Win Rate: The backtesting library reports {backtest_stats.get('Win Rate [%]', 'N/A')}%, " +
          f"while our calculation shows {custom_metrics.get('Win Rate (%)', 'N/A'):.2f}%.")
    
    print("\nRecommendation:")
    print("For critical trading decisions, rely on the custom metrics calculated directly from trade data.")
    print("The backtesting library's metrics may be useful for relative comparisons between strategies,")
    print("but the absolute values should be verified with independent calculations.")

def export_trade_log(bt, stats, config):
    """Export trade log to CSV."""
    print("\n--- Exporting Trade Log to CSV ---")

    strategy_instance = bt._strategy
    output_config = config['output']

    # Check if strategy has internal trade log
    if hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log:
        print(f"Strategy has internal trade log with {len(strategy_instance.trade_log)} entries")

        # Create DataFrame from trade log
        trades_df = pd.DataFrame(strategy_instance.trade_log)

        # Reorder columns to match required format
        available_columns = [col for col in output_config['required_trade_columns'] if col in trades_df.columns]
        trades_df = trades_df[available_columns]

        # Export to CSV
        output_file = output_config['trade_log_file']
        trades_df.to_csv(output_file, index=False)
        print(f"Trade log exported to: {output_file} (using strategy's internal trade log)")
        print(f"Total trades exported: {len(trades_df)}")
    else:
        # Use backtesting framework's trade data as fallback
        print("Using backtesting framework's trade data instead")
        all_trades = stats['_trades']
        if not all_trades.empty:
            trades_list = []
            for idx, trade in all_trades.iterrows():
                entry_time = trade.EntryTime
                exit_time = trade.ExitTime
                entry_price = trade.EntryPrice
                exit_price = trade.ExitPrice
                position_type = "LONG" if trade.Size > 0 else "SHORT"
                duration = exit_time - entry_time

                # Calculate profit in points
                if position_type == "LONG":
                    profit_points = exit_price - entry_price
                else:  # SHORT
                    profit_points = entry_price - exit_price

                profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0

                # Determine exit reason based on profit/loss
                exit_time_str = exit_time.strftime('%H:%M')
                if exit_time_str >= "15:15":
                    exit_reason = "Time-based Exit"
                # For profitable trades, it's a take profit
                elif profit_points > 0:
                    exit_reason = "Take Profit"
                # For losing trades, it's a stop loss
                elif profit_points < 0:
                    exit_reason = "Stop Loss"
                else:
                    exit_reason = "Manual Exit"

                trades_list.append({
                    "Index": idx + 1,
                    "Entry DateTime": entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Exit DateTime": exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Entry Price": round(entry_price, 2),
                    "Exit Price": round(exit_price, 2),
                    "Profit Points": round(profit_points, 2),
                    "Profit Percent": round(profit_percent, 2),
                    "Position": position_type,
                    "Trade Duration": str(duration),
                    "Exit Reason": exit_reason
                })

            # Create DataFrame and export to CSV
            trades_df = pd.DataFrame(trades_list)
            output_file = output_config['trade_log_file']
            trades_df.to_csv(output_file, index=False)
            print(f"Trade log exported to: {output_file}")
            print(f"Total trades exported: {len(trades_df)}")
        else:
            print("No trades were executed, so no trade log is available to export.")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run Simple Price Action Strategy with specified timeframe')
    parser.add_argument('--timeframe', type=int, choices=[1, 5, 10, 15, 30, 60], default=5,
                        help='Timeframe in minutes (1, 5, 10, 15, 30 or 60)')
    parser.add_argument('--config', type=str, default='SimplePriceActionStrategyConfig.yaml',
                        help='Path to configuration file')
    return parser.parse_args()

def update_config(config_path, timeframe):
    """Update the configuration file with the specified timeframe."""
    # Create a backup of the original config file
    backup_path = f"{config_path}.backup"
    os.system(f"copy {config_path} {backup_path}")
    
    # Load the configuration
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Update the timeframe
    config['data']['timeframe'] = timeframe
    
    # Save the updated configuration
    with open(config_path, 'w') as f:
        yaml.dump(config, f)
    
    return backup_path

def restore_config(config_path, backup_path):
    """Restore the original configuration file."""
    os.system(f"copy {backup_path} {config_path}")
    os.system(f"del {backup_path}")

def main(config_path=None, specified_timeframe=None):
    """Main execution function."""
    # If config_path is not provided, use the default or command line argument
    if config_path is None:
        # Check if running from command line with arguments
        if len(sys.argv) > 1:
            args = parse_arguments()
            config_path = args.config
            specified_timeframe = args.timeframe
            print(f"\nRunning Simple Price Action Strategy with {specified_timeframe}-minute timeframe")
        else:
            config_path = "SimplePriceActionStrategyConfig.yaml"
    
    # Load configuration
    config = load_config(config_path)
    
    # If timeframe is specified, update the config temporarily
    backup_path = None
    if specified_timeframe is not None:
        backup_path = update_config(config_path, specified_timeframe)
        # Reload the config with the updated timeframe
        config = load_config(config_path)
    
    # Get timeframe from config
    timeframe = config['data'].get('timeframe', 1)
    
    # Update output file name to include timeframe
    if timeframe > 1:
        base_filename = config['output']['trade_log_file']
        filename_parts = base_filename.split('.')
        new_filename = f"{filename_parts[0]}_{timeframe}min.{filename_parts[1]}"
        config['output']['trade_log_file'] = new_filename
        
        # Also update the JSON stats filename
        global_stats_filename = f"backtest_stats_{timeframe}min.json"
    else:
        global_stats_filename = "backtest_stats.json"
    
    print("\n" + "="*80)
    print(f" "*20 + f"SIMPLE PRICE ACTION STRATEGY - {timeframe}min TIMEFRAME")
    print("="*80)

    # Load and process data
    data = load_and_process_data(config)

    # Run backtest
    bt, stats = run_backtest(config, data)

    # Print detailed metrics from backtesting library
    print_detailed_metrics(stats, config)

    # Export trade log
    export_trade_log(bt, stats, config)
    
    # Get the trade log file path from config
    trade_log_file = config['output']['trade_log_file']
    
    print("\n" + "="*80)
    print(" "*20 + "CUSTOM METRICS CALCULATION")
    print("="*80)
    
    # Save stats to JSON file with timeframe in the filename
    try:
        # Convert stats to a dictionary (excluding non-serializable objects)
        stats_dict = {k: v for k, v in stats.items() if isinstance(v, (int, float, str, bool, list, dict)) or v is None}
        
        # Save to JSON file
        with open(global_stats_filename, 'w') as f:
            json.dump(stats_dict, f, indent=4)
        
        print(f"Backtest statistics saved to '{global_stats_filename}'")
    except Exception as e:
        print(f"Warning: Could not save backtest statistics to JSON: {e}")
    
    # Load backtest stats from JSON file
    try:
        with open(global_stats_filename, 'r') as f:
            backtest_stats = json.load(f)
    except Exception as e:
        print(f"Warning: Could not load backtest statistics from JSON: {e}")
        backtest_stats = None
    
    # Analyze strategy performance using custom metrics
    print("\nCalculating custom metrics from trade log data...")
    custom_metrics = analyze_strategy_performance(trade_log_file, config)
    
    # Compare metrics if both are available
    if custom_metrics and backtest_stats:
        print("\nComparing backtesting library metrics with custom metrics...")
        compare_metrics(custom_metrics, backtest_stats)
    
    print("\n" + "="*80)
    print(f" "*20 + f"BACKTEST COMPLETED - {timeframe}min TIMEFRAME")
    print("="*80)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"Error running strategy: {e}")
    finally:
        # If we're running with command line arguments and have a backup, restore it
        if len(sys.argv) > 1:
            args = parse_arguments()
            backup_path = f"{args.config}.backup"
            if os.path.exists(backup_path):
                restore_config(args.config, backup_path)
                print(f"\nRestored original configuration file: {args.config}")
        
        # Print completion message if using command line arguments
        if len(sys.argv) > 1:
            args = parse_arguments()
            print(f"\nBacktest completed for {args.timeframe}-minute timeframe")
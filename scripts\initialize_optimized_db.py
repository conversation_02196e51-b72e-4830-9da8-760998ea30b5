#!/usr/bin/env python3
"""
Initialize the optimized TimescaleDB setup.
This script sets up the complete optimized database schema, compression, and monitoring.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.init_optimized_db import initialize_optimized_database
from app.database.aggregation_jobs import initialize_aggregation_system
from app.database.monitoring import TimescaleDBMonitor

logger = get_logger(__name__)


def main():
    """Main initialization function."""
    logger.info("🚀 Starting optimized TimescaleDB initialization...")
    logger.info("=" * 80)
    
    try:
        # Step 1: Initialize optimized database schema
        logger.info("Step 1: Initializing optimized database schema...")
        if not initialize_optimized_database():
            logger.error("❌ Failed to initialize optimized database")
            return False
        
        logger.info("✅ Optimized database schema initialized successfully")
        
        # Step 2: Initialize aggregation system
        logger.info("\nStep 2: Initializing aggregation system...")
        if not initialize_aggregation_system():
            logger.error("❌ Failed to initialize aggregation system")
            return False
        
        logger.info("✅ Aggregation system initialized successfully")
        
        # Step 3: Generate initial health report
        logger.info("\nStep 3: Generating initial health report...")
        monitor = TimescaleDBMonitor()
        report = monitor.generate_health_report()
        
        logger.info("📊 INITIAL HEALTH REPORT")
        logger.info("-" * 60)
        logger.info(f"Timestamp: {report['timestamp']}")
        
        # Display chunk statistics
        if report['chunk_statistics']:
            logger.info("\n📦 Chunk Statistics:")
            for stat in report['chunk_statistics']:
                logger.info(f"  {stat['hypertable_name']}: {stat['total_chunks']} chunks")
        
        # Display table sizes
        if report['table_sizes']:
            logger.info("\n💾 Table Sizes:")
            for size in report['table_sizes']:
                logger.info(f"  {size['tablename']}: {size['total_size']}")
        
        # Display job statistics
        if report['job_statistics']:
            logger.info("\n⚙️  Background Jobs:")
            for job in report['job_statistics']:
                logger.info(f"  Job {job['job_id']}: {job['application_name']}")
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 OPTIMIZED TIMESCALEDB SETUP COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        
        logger.info("\n📋 NEXT STEPS:")
        logger.info("1. Run the data loading script: python scripts/load_all_symbols_15year_data.py --symbols indices")
        logger.info("2. Monitor performance: python app/database/monitoring.py")
        logger.info("3. Run maintenance: python app/database/monitoring.py maintenance")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Initialization failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

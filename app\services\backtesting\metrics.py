"""
Performance metrics calculation for backtesting.
"""

from typing import List, Dict, Any, Optional
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from .portfolio import Portfolio, PortfolioSnapshot
from .strategy import Trade
from app.core.logging import get_logger

logger = get_logger(__name__)


class PerformanceMetrics:
    """Calculate comprehensive performance metrics for backtesting results."""
    
    def __init__(self, portfolio: Portfolio, benchmark_returns: Optional[List[float]] = None):
        """
        Initialize performance metrics calculator.
        
        Args:
            portfolio: Portfolio instance
            benchmark_returns: Optional benchmark returns for comparison
        """
        self.portfolio = portfolio
        self.benchmark_returns = benchmark_returns or []
        self.snapshots = portfolio.snapshots
        self.trades = portfolio.trades
    
    def calculate_all_metrics(self) -> Dict[str, Any]:
        """Calculate all performance metrics."""
        try:
            # Basic metrics
            basic_metrics = self._calculate_basic_metrics()
            
            # Risk metrics
            risk_metrics = self._calculate_risk_metrics()
            
            # Trade analysis
            trade_metrics = self._calculate_trade_metrics()
            
            # Time-based metrics
            time_metrics = self._calculate_time_metrics()
            
            # Drawdown analysis
            drawdown_metrics = self._calculate_drawdown_metrics()
            
            # Ratios
            ratio_metrics = self._calculate_ratio_metrics()
            
            return {
                **basic_metrics,
                **risk_metrics,
                **trade_metrics,
                **time_metrics,
                **drawdown_metrics,
                **ratio_metrics,
                'calculation_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {'error': str(e)}
    
    def _calculate_basic_metrics(self) -> Dict[str, Any]:
        """Calculate basic performance metrics."""
        if not self.snapshots:
            return {
                'total_return': 0.0,
                'annualized_return': 0.0,
                'total_pnl': 0.0,
                'initial_capital': self.portfolio.initial_cash,
                'final_value': self.portfolio.total_value
            }
        
        initial_value = self.portfolio.initial_cash
        final_value = self.portfolio.total_value
        total_return = ((final_value - initial_value) / initial_value) * 100
        
        # Calculate annualized return
        if len(self.snapshots) > 1:
            start_date = self.snapshots[0].timestamp
            end_date = self.snapshots[-1].timestamp
            days = (end_date - start_date).days
            years = days / 365.25
            
            if years > 0:
                annualized_return = (((final_value / initial_value) ** (1 / years)) - 1) * 100
            else:
                annualized_return = 0.0
        else:
            annualized_return = 0.0
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'total_pnl': self.portfolio.total_pnl,
            'realized_pnl': self.portfolio.realized_pnl,
            'unrealized_pnl': self.portfolio.unrealized_pnl,
            'initial_capital': initial_value,
            'final_value': final_value,
            'total_commission': self.portfolio.total_commission_paid
        }
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate risk-related metrics."""
        if len(self.snapshots) < 2:
            return {
                'volatility': 0.0,
                'downside_deviation': 0.0,
                'var_95': 0.0,
                'cvar_95': 0.0
            }
        
        # Calculate daily returns
        returns = []
        for i in range(1, len(self.snapshots)):
            prev_value = self.snapshots[i-1].total_value
            curr_value = self.snapshots[i].total_value
            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)
        
        if not returns:
            return {
                'volatility': 0.0,
                'downside_deviation': 0.0,
                'var_95': 0.0,
                'cvar_95': 0.0
            }
        
        returns_array = np.array(returns)
        
        # Volatility (annualized)
        volatility = np.std(returns_array) * np.sqrt(252) * 100
        
        # Downside deviation
        negative_returns = returns_array[returns_array < 0]
        downside_deviation = np.std(negative_returns) * np.sqrt(252) * 100 if len(negative_returns) > 0 else 0.0
        
        # Value at Risk (95%)
        var_95 = np.percentile(returns_array, 5) * 100
        
        # Conditional Value at Risk (95%)
        var_threshold = np.percentile(returns_array, 5)
        tail_returns = returns_array[returns_array <= var_threshold]
        cvar_95 = np.mean(tail_returns) * 100 if len(tail_returns) > 0 else 0.0
        
        return {
            'volatility': volatility,
            'downside_deviation': downside_deviation,
            'var_95': var_95,
            'cvar_95': cvar_95
        }
    
    def _calculate_trade_metrics(self) -> Dict[str, Any]:
        """Calculate trade-related metrics."""
        if not self.trades:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'avg_trade_pnl': 0.0,
                'avg_winning_trade': 0.0,
                'avg_losing_trade': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0,
                'profit_factor': 0.0,
                'avg_trade_duration': 0.0,
                'risk_reward_ratio': 0.0,
                'avg_risk_reward_ratio': 0.0,
                'expectancy': 0.0
            }

        winning_trades = [t for t in self.trades if t.is_profitable]
        losing_trades = [t for t in self.trades if not t.is_profitable]

        total_pnl = sum(t.pnl for t in self.trades)
        gross_profit = sum(t.pnl for t in winning_trades)
        gross_loss = abs(sum(t.pnl for t in losing_trades))

        # Calculate Risk-to-Reward ratio metrics
        avg_winning_trade_pnl = gross_profit / len(winning_trades) if winning_trades else 0.0
        avg_losing_trade_pnl = gross_loss / len(losing_trades) if losing_trades else 0.0

        # Overall Risk-to-Reward ratio (average win / average loss)
        risk_reward_ratio = avg_winning_trade_pnl / avg_losing_trade_pnl if avg_losing_trade_pnl > 0 else 0.0

        # Calculate individual trade R:R ratios and average
        individual_rr_ratios = []
        for trade in self.trades:
            if hasattr(trade, 'risk_amount') and hasattr(trade, 'reward_amount'):
                if trade.risk_amount > 0:
                    individual_rr_ratios.append(trade.reward_amount / trade.risk_amount)
            elif trade.pnl != 0:
                # Fallback: use absolute PnL values
                if trade.pnl < 0:  # Losing trade
                    individual_rr_ratios.append(0.0)
                else:  # Winning trade - estimate R:R based on average loss
                    if avg_losing_trade_pnl > 0:
                        individual_rr_ratios.append(trade.pnl / avg_losing_trade_pnl)

        avg_risk_reward_ratio = np.mean(individual_rr_ratios) if individual_rr_ratios else 0.0

        # Calculate expectancy
        win_rate = len(winning_trades) / len(self.trades)
        loss_rate = len(losing_trades) / len(self.trades)
        expectancy = (win_rate * avg_winning_trade_pnl) - (loss_rate * avg_losing_trade_pnl)

        return {
            'total_trades': len(self.trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate * 100,
            'avg_trade_pnl': total_pnl / len(self.trades),
            'avg_winning_trade': avg_winning_trade_pnl,
            'avg_losing_trade': -avg_losing_trade_pnl,
            'largest_win': max(t.pnl for t in self.trades),
            'largest_loss': min(t.pnl for t in self.trades),
            'profit_factor': gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0.0,
            'avg_trade_duration': np.mean([t.duration for t in self.trades]),
            'risk_reward_ratio': risk_reward_ratio,
            'avg_risk_reward_ratio': avg_risk_reward_ratio,
            'expectancy': expectancy
        }
    
    def _calculate_time_metrics(self) -> Dict[str, Any]:
        """Calculate time-based metrics."""
        if not self.snapshots:
            return {
                'backtest_duration_days': 0,
                'trading_days': 0,
                'start_date': None,
                'end_date': None
            }
        
        start_date = self.snapshots[0].timestamp
        end_date = self.snapshots[-1].timestamp
        duration = (end_date - start_date).days
        
        return {
            'backtest_duration_days': duration,
            'trading_days': len(self.snapshots),
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        }
    
    def _calculate_drawdown_metrics(self) -> Dict[str, Any]:
        """Calculate drawdown-related metrics."""
        if len(self.snapshots) < 2:
            return {
                'max_drawdown': 0.0,
                'max_drawdown_duration': 0,
                'current_drawdown': 0.0,
                'recovery_factor': 0.0
            }
        
        # Calculate running maximum and drawdowns
        values = [s.total_value for s in self.snapshots]
        running_max = np.maximum.accumulate(values)
        drawdowns = (values - running_max) / running_max * 100
        
        max_drawdown = abs(min(drawdowns))
        current_drawdown = abs(drawdowns[-1])
        
        # Calculate maximum drawdown duration
        max_dd_duration = 0
        current_dd_duration = 0
        
        for dd in drawdowns:
            if dd < 0:
                current_dd_duration += 1
                max_dd_duration = max(max_dd_duration, current_dd_duration)
            else:
                current_dd_duration = 0
        
        # Recovery factor
        total_return = ((values[-1] - values[0]) / values[0]) * 100
        recovery_factor = total_return / max_drawdown if max_drawdown > 0 else 0.0
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_duration': max_dd_duration,
            'current_drawdown': current_drawdown,
            'recovery_factor': recovery_factor
        }
    
    def _calculate_ratio_metrics(self) -> Dict[str, Any]:
        """Calculate various financial ratios."""
        if len(self.snapshots) < 2:
            return {
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'calmar_ratio': 0.0,
                'information_ratio': 0.0
            }
        
        # Calculate daily returns
        returns = []
        for i in range(1, len(self.snapshots)):
            prev_value = self.snapshots[i-1].total_value
            curr_value = self.snapshots[i].total_value
            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)
        
        if not returns:
            return {
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'calmar_ratio': 0.0,
                'information_ratio': 0.0
            }
        
        returns_array = np.array(returns)
        
        # Sharpe Ratio (assuming risk-free rate of 2% annually)
        risk_free_rate = 0.02 / 252  # Daily risk-free rate
        excess_returns = returns_array - risk_free_rate
        sharpe_ratio = (np.mean(excess_returns) / np.std(returns_array)) * np.sqrt(252) if np.std(returns_array) > 0 else 0.0
        
        # Sortino Ratio
        negative_returns = returns_array[returns_array < risk_free_rate]
        downside_std = np.std(negative_returns) if len(negative_returns) > 0 else 0.0
        sortino_ratio = (np.mean(excess_returns) / downside_std) * np.sqrt(252) if downside_std > 0 else 0.0
        
        # Calmar Ratio
        annualized_return = (np.mean(returns_array) * 252) * 100
        max_drawdown = self.portfolio.max_drawdown
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0.0
        
        # Information Ratio (vs benchmark if available)
        information_ratio = 0.0
        if self.benchmark_returns and len(self.benchmark_returns) == len(returns):
            benchmark_array = np.array(self.benchmark_returns)
            active_returns = returns_array - benchmark_array
            tracking_error = np.std(active_returns)
            information_ratio = np.mean(active_returns) / tracking_error if tracking_error > 0 else 0.0
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'information_ratio': information_ratio
        }

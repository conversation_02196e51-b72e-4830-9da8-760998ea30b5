#!/usr/bin/env python3
"""
Fix timezone issues in PostgreSQL tables.
Remove +5:30 timezone from datetime and created_at columns.
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import get_settings
from app.core.logging import get_logger

logger = get_logger(__name__)

def fix_timezone_issues():
    """
    Fix timezone issues in PostgreSQL tables by:
    1. Creating new table with correct schema
    2. Migrating data without timezone info
    3. Recreating hypertable with proper configuration
    """
    database_settings = get_settings().database
    engine = create_engine(database_settings.url)

    logger.info("🔧 Starting timezone fixes for PostgreSQL tables...")

    try:
        with engine.connect() as conn:
            # Step 1: Check current table structure
            logger.info("📋 Checking current table structure...")
            result = conn.execute(text("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'stock_ohlcv'
                AND column_name IN ('datetime', 'created_at')
                ORDER BY column_name;
            """))

            current_columns = {row[0]: row[1] for row in result}
            logger.info(f"Current column types: {current_columns}")

            # Check if we need to fix timezone issues
            needs_fix = any(col_type == 'timestamp with time zone' for col_type in current_columns.values())

            if not needs_fix:
                logger.info("✅ No timezone issues found - columns already have correct types")
                return True

            logger.info("🔄 Timezone issues detected - proceeding with migration...")

            # Step 2: Create backup table with correct schema
            logger.info("📦 Creating backup table with correct schema...")
            conn.execute(text("""
                CREATE TABLE stock_ohlcv_backup AS
                SELECT
                    symbol,
                    exchange,
                    interval,
                    datetime AT TIME ZONE 'UTC' AS datetime,
                    open,
                    high,
                    low,
                    close,
                    volume,
                    created_at AT TIME ZONE 'UTC' AS created_at
                FROM stock_ohlcv;
            """))
            logger.info("✅ Backup table created with timezone-naive timestamps")

            # Step 3: Get record count for verification
            count_result = conn.execute(text("SELECT COUNT(*) FROM stock_ohlcv;"))
            original_count = count_result.scalar()
            logger.info(f"📊 Original table has {original_count:,} records")

            backup_count_result = conn.execute(text("SELECT COUNT(*) FROM stock_ohlcv_backup;"))
            backup_count = backup_count_result.scalar()
            logger.info(f"📊 Backup table has {backup_count:,} records")

            if original_count != backup_count:
                logger.error(f"❌ Record count mismatch! Original: {original_count}, Backup: {backup_count}")
                return False

            # Step 4: Drop the original hypertable
            logger.info("🗑️ Dropping original hypertable...")
            conn.execute(text("DROP TABLE stock_ohlcv CASCADE;"))
            logger.info("✅ Original hypertable dropped")

            # Step 5: Create new table with correct schema
            logger.info("🏗️ Creating new table with correct schema...")
            conn.execute(text("""
                CREATE TABLE stock_ohlcv (
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL DEFAULT 'NSE',
                    interval TEXT NOT NULL DEFAULT '1m',
                    datetime TIMESTAMP WITHOUT TIME ZONE NOT NULL,
                    open FLOAT NOT NULL,
                    high FLOAT NOT NULL,
                    low FLOAT NOT NULL,
                    close FLOAT NOT NULL,
                    volume BIGINT NOT NULL,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (NOW() AT TIME ZONE 'UTC'),
                    PRIMARY KEY (symbol, interval, datetime)
                );
            """))
            logger.info("✅ New table created with TIMESTAMP WITHOUT TIME ZONE")

            # Step 6: Recreate hypertable
            logger.info("⚡ Converting to hypertable...")
            conn.execute(text("""
                SELECT create_hypertable(
                    'stock_ohlcv',
                    'datetime',
                    partitioning_column => 'symbol',
                    number_partitions => 20,
                    chunk_time_interval => INTERVAL '30 days',
                    if_not_exists => TRUE
                );
            """))
            logger.info("✅ Hypertable created with optimized configuration")

            # Step 7: Migrate data from backup
            logger.info("📥 Migrating data from backup table...")
            conn.execute(text("""
                INSERT INTO stock_ohlcv
                SELECT * FROM stock_ohlcv_backup;
            """))

            # Verify migration
            new_count_result = conn.execute(text("SELECT COUNT(*) FROM stock_ohlcv;"))
            new_count = new_count_result.scalar()
            logger.info(f"📊 New table has {new_count:,} records")

            if new_count != original_count:
                logger.error(f"❌ Migration failed! Expected: {original_count}, Got: {new_count}")
                return False

            logger.info("✅ Data migration completed successfully")

            # Step 8: Drop backup table
            logger.info("🧹 Cleaning up backup table...")
            conn.execute(text("DROP TABLE stock_ohlcv_backup;"))
            logger.info("✅ Backup table cleaned up")

            # Step 9: Recreate indexes for performance
            logger.info("🔍 Recreating performance indexes...")
            indexes = [
                "CREATE INDEX idx_stock_ohlcv_symbol_datetime ON stock_ohlcv (symbol, datetime DESC);",
                "CREATE INDEX idx_stock_ohlcv_exchange_datetime ON stock_ohlcv (exchange, datetime DESC);",
                "CREATE INDEX idx_stock_ohlcv_interval_datetime ON stock_ohlcv (interval, datetime DESC);",
                "CREATE INDEX idx_stock_ohlcv_symbol_interval_datetime ON stock_ohlcv (symbol, interval, datetime DESC);",
                "CREATE INDEX idx_stock_ohlcv_symbol_volume ON stock_ohlcv (symbol, volume DESC);",
                "CREATE INDEX idx_stock_ohlcv_volume_datetime ON stock_ohlcv (volume DESC, datetime DESC);"
            ]

            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                except Exception as e:
                    logger.warning(f"Index creation warning (may already exist): {e}")

            logger.info("✅ Performance indexes recreated")

            # Commit all changes
            conn.commit()
            logger.info("✅ All timezone fixes completed successfully!")

            return True
                
    except Exception as e:
        logger.error(f"❌ Failed to fix timezone issues: {e}")
        return False

def verify_timezone_fixes():
    """Verify that timezone fixes were applied correctly."""
    database_settings = get_settings().database
    engine = create_engine(database_settings.url)
    
    logger.info("🔍 Verifying timezone fixes...")
    
    try:
        with engine.connect() as conn:
            # Check column types
            result = conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'stock_ohlcv' 
                AND column_name IN ('datetime', 'created_at')
                ORDER BY column_name;
            """))
            
            updated_columns = {row[0]: row[1] for row in result}
            logger.info(f"Updated column types: {updated_columns}")
            
            # Check sample data
            sample_data = conn.execute(text("""
                SELECT symbol, datetime, created_at 
                FROM stock_ohlcv 
                WHERE symbol = 'RELIANCE' 
                ORDER BY datetime DESC 
                LIMIT 3;
            """))
            
            logger.info("📊 Sample data after timezone fix:")
            for row in sample_data:
                logger.info(f"   {row[0]} | {row[1]} | {row[2]}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Failed to verify timezone fixes: {e}")
        return False

def main():
    """Main execution function."""
    try:
        success = fix_timezone_issues()
        if success:
            verify_timezone_fixes()
            logger.info("🎉 Timezone fixes completed successfully!")
        else:
            logger.error("❌ Timezone fixes failed!")
            return False
        return True
    except Exception as e:
        logger.error(f"❌ Script execution failed: {e}")
        return False

if __name__ == "__main__":
    main()

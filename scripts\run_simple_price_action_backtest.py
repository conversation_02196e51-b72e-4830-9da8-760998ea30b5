#!/usr/bin/env python3
"""
Run Simple Price Action Strategy backtest using database data.
Integrated version of SimplePriceActionStrategyMain.py with our backtesting framework.
"""

import argparse
import sys
import yaml
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd

from app.core.logging import get_logger
from app.core.config import get_settings
from app.database.connection import get_db
from app.services.optimized_data_service import OptimizedDataService
from app.services.backtesting.engine import BacktestEngine
from app.services.backtesting.simple_price_action_strategy import SimplePriceActionStrategy, PriceActionConfig
from app.services.backtesting.metrics import PerformanceMetrics

logger = get_logger(__name__)


class SimplePriceActionBacktester:
    """Main class for running Simple Price Action Strategy backtests."""
    
    def __init__(self):
        """Initialize the backtester."""
        self.db = None
        self.data_service = None
        self.engine = None
        
    def initialize_services(self) -> bool:
        """Initialize database and services."""
        try:
            # Initialize database connection
            self.db = next(get_db())
            self.data_service = OptimizedDataService(self.db)
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def load_config(self, config_path: str) -> dict:
        """Load configuration from YAML file."""
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                # Use default config if file doesn't exist
                logger.warning(f"Config file {config_path} not found, using default configuration")
                return self._get_default_config()
            
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"✓ Configuration loaded from {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> dict:
        """Get default configuration."""
        return {
            'backtest': {
                'initial_capital': 100000,
                'commission': 0.001,
                'slippage': 0.001
            },
            'strategy': {
                'atr_period': 10,
                'fast_ma_period': 5,
                'slow_ma_period': 21,
                'rsi_period': 10,
                'rsi_overbought': 70,
                'rsi_oversold': 30,
                'sl_atr_multiplier': 2.0,
                'tp_rr': 2.0,
                'position_size': 0.1
            },
            'data': {
                'symbol': 'RELIANCE',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31',
                'timeframe': '1m'
            }
        }
    
    def load_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1m') -> pd.DataFrame:
        """Load OHLCV data from database."""
        try:
            logger.info(f"Loading data for {symbol} from {start_date} to {end_date}")
            
            # Convert date strings to datetime
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            # Get data from database
            data = self.data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_dt,
                end_time=end_dt,
                interval=timeframe,
                as_dataframe=True
            )
            
            if data is None or data.empty:
                logger.error(f"No data found for {symbol}")
                return pd.DataFrame()
            
            # Ensure proper column names for backtesting (lowercase for engine compatibility)
            data.columns = ['open', 'high', 'low', 'close', 'volume']

            # Ensure datetime index
            if not isinstance(data.index, pd.DatetimeIndex):
                data.index = pd.to_datetime(data.index)
            
            logger.info(f"✓ Loaded {len(data)} data points for {symbol}")
            logger.info(f"   Date range: {data.index[0]} to {data.index[-1]}")
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to load data: {e}")
            return pd.DataFrame()
    
    def resample_data(self, data: pd.DataFrame, timeframe: int) -> pd.DataFrame:
        """Resample data to specified timeframe in minutes."""
        if timeframe <= 1:
            return data
        
        try:
            logger.info(f"Resampling data to {timeframe}-minute timeframe...")
            
            # Define resampling rule
            rule = f'{timeframe}T'  # T stands for minutes
            
            # Resample OHLCV data
            resampled = pd.DataFrame()
            resampled['Open'] = data['Open'].resample(rule).first()
            resampled['High'] = data['High'].resample(rule).max()
            resampled['Low'] = data['Low'].resample(rule).min()
            resampled['Close'] = data['Close'].resample(rule).last()
            resampled['Volume'] = data['Volume'].resample(rule).sum()
            
            # Drop NaN values
            resampled.dropna(inplace=True)
            
            logger.info(f"✓ Resampled to {len(resampled)} data points")
            return resampled
            
        except Exception as e:
            logger.error(f"Failed to resample data: {e}")
            return data
    
    def run_backtest(self, config: dict) -> dict:
        """Run the backtest with given configuration."""
        try:
            # Load data
            data_config = config.get('data', {})
            symbol = data_config.get('symbol', 'RELIANCE')
            start_date = data_config.get('start_date', '2024-01-01')
            end_date = data_config.get('end_date', '2024-12-31')
            timeframe_minutes = data_config.get('timeframe', 1)
            
            data = self.load_data(symbol, start_date, end_date)
            if data.empty:
                logger.error("No data available for backtesting")
                return {}
            
            # Resample data if needed
            if timeframe_minutes > 1:
                data = self.resample_data(data, timeframe_minutes)
            
            # Initialize strategy
            strategy = SimplePriceActionStrategy(config)

            # Initialize backtest engine
            backtest_config = config.get('backtest', {})
            self.engine = BacktestEngine(
                initial_cash=backtest_config.get('initial_capital', 100000),
                commission_rate=backtest_config.get('commission', 0.001),
                slippage=backtest_config.get('slippage', 0.001)
            )

            # Set strategy and data
            self.engine.set_strategy(strategy)

            # Convert data format for engine (add timestamp column)
            data_for_engine = data.copy()
            data_for_engine['timestamp'] = data_for_engine.index
            data_for_engine = data_for_engine.reset_index(drop=True)

            self.engine.set_data(data_for_engine)

            logger.info("🚀 Starting backtest...")

            # Run backtest
            results = self.engine.run_backtest(symbol=symbol)
            
            # Calculate performance metrics
            metrics = PerformanceMetrics(self.engine.portfolio)
            performance = metrics.calculate_all_metrics()
            
            logger.info("✅ Backtest completed successfully")
            
            return {
                'results': results,
                'performance': performance,
                'config': config
            }
            
        except Exception as e:
            logger.error(f"Backtest failed: {e}")
            return {}
    
    def print_results(self, backtest_results: dict) -> None:
        """Print backtest results."""
        if not backtest_results:
            logger.error("No results to display")
            return
        
        performance = backtest_results.get('performance', {})
        config = backtest_results.get('config', {})
        
        print("\n" + "="*80)
        print(" "*20 + "SIMPLE PRICE ACTION STRATEGY BACKTEST RESULTS")
        print("="*80)
        
        # Strategy info
        strategy_config = config.get('strategy', {})
        print(f"\n📊 Strategy Configuration:")
        print(f"   ATR Period: {strategy_config.get('atr_period', 10)}")
        print(f"   Fast MA Period: {strategy_config.get('fast_ma_period', 5)}")
        print(f"   Slow MA Period: {strategy_config.get('slow_ma_period', 21)}")
        print(f"   RSI Period: {strategy_config.get('rsi_period', 10)}")
        print(f"   Stop Loss ATR Multiplier: {strategy_config.get('sl_atr_multiplier', 2.0)}")
        print(f"   Take Profit Risk/Reward: {strategy_config.get('tp_rr', 2.0)}")
        
        # Performance metrics
        print(f"\n📈 Performance Metrics:")
        print(f"   Total Return: {performance.get('total_return', 0):.2f}%")
        print(f"   Annualized Return: {performance.get('annualized_return', 0):.2f}%")
        print(f"   Max Drawdown: {performance.get('max_drawdown', 0):.2f}%")
        print(f"   Sharpe Ratio: {performance.get('sharpe_ratio', 0):.2f}")
        print(f"   Win Rate: {performance.get('win_rate', 0):.2f}%")
        print(f"   Profit Factor: {performance.get('profit_factor', 0):.2f}")
        print(f"   Total Trades: {performance.get('total_trades', 0)}")
        
        print("\n" + "="*80)
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run Simple Price Action Strategy Backtest')
    parser.add_argument('--config', type=str, default='config/simple_price_action_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--symbol', type=str, help='Symbol to backtest (overrides config)')
    parser.add_argument('--start-date', type=str, help='Start date (YYYY-MM-DD, overrides config)')
    parser.add_argument('--end-date', type=str, help='End date (YYYY-MM-DD, overrides config)')
    parser.add_argument('--timeframe', type=int, help='Timeframe in minutes (overrides config)')
    
    return parser.parse_args()


def main():
    """Main execution function."""
    try:
        args = parse_arguments()
        
        logger.info("🚀 Simple Price Action Strategy Backtest")
        logger.info("=" * 80)
        
        # Initialize backtester
        backtester = SimplePriceActionBacktester()
        
        # Initialize services
        logger.info("Step 1: Initializing services...")
        if not backtester.initialize_services():
            return False
        
        # Load configuration
        logger.info("Step 2: Loading configuration...")
        config = backtester.load_config(args.config)
        
        # Override config with command line arguments
        if args.symbol:
            config.setdefault('data', {})['symbol'] = args.symbol
        if args.start_date:
            config.setdefault('data', {})['start_date'] = args.start_date
        if args.end_date:
            config.setdefault('data', {})['end_date'] = args.end_date
        if args.timeframe:
            config.setdefault('data', {})['timeframe'] = args.timeframe
        
        # Run backtest
        logger.info("Step 3: Running backtest...")
        results = backtester.run_backtest(config)
        
        if results:
            # Print results
            logger.info("Step 4: Displaying results...")
            backtester.print_results(results)
        else:
            logger.error("Backtest failed - no results generated")
            return False
        
        return True
        
    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        if 'backtester' in locals():
            backtester.cleanup()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

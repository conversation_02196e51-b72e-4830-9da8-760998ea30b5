#!/usr/bin/env python3
"""
Performance comparison between reference backtesting library and our custom engine.
Tests both systems with the same data source and strategy for accurate comparison.
"""

import time
import json
import pandas as pd
import psutil
import os
from datetime import datetime, timedelta
from pathlib import Path
import sys
import tracemalloc

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db
from app.services.optimized_data_service import OptimizedDataService

logger = get_logger(__name__)


class PerformanceProfiler:
    """Performance profiling utility."""

    def __init__(self, name: str):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.start_memory = None
        self.peak_memory = None
        self.execution_time = None
        self.memory_used = None
        self.process = psutil.Process()

    def __enter__(self):
        """Start profiling."""
        tracemalloc.start()
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        logger.info(f"🚀 Starting {self.name} - Memory: {self.start_memory:.1f} MB")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """End profiling."""
        self.end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = peak / 1024 / 1024  # MB

        self.execution_time = self.end_time - self.start_time
        self.memory_used = end_memory - self.start_memory

        logger.info(f"✅ {self.name} completed:")
        logger.info(f"   ⏱️  Execution time: {self.execution_time:.2f} seconds")
        logger.info(f"   💾 Memory used: {self.memory_used:.1f} MB")
        logger.info(f"   📊 Peak memory: {self.peak_memory:.1f} MB")


def run_reference_backtest(symbol: str = "NIFTY50", timeframe: int = 30, test_period_days: int = 30):
    """
    Run the reference backtesting library version.

    Args:
        symbol: Symbol to test
        timeframe: Timeframe in minutes
        test_period_days: Number of days to test (for short-term comparison)

    Returns:
        Performance results dictionary
    """
    logger.info(f"🔬 Running Reference Backtest (backtesting library)")

    with PerformanceProfiler("Reference Backtest") as profiler:
        try:
            # Import reference modules
            reference_dir = project_root / "Reference" / "V7_IntradayCleanup_Best_30Min"
            sys.path.insert(0, str(reference_dir))

            # Change to reference directory for config file access
            original_cwd = os.getcwd()
            os.chdir(str(reference_dir))

            try:
                from SimplePriceActionStrategyMain_DB import main_db_version

                # Run the reference backtest
                result = main_db_version(timeframe=timeframe)

                return {
                    'engine': 'backtesting_library',
                    'execution_time': profiler.execution_time,
                    'memory_used': profiler.memory_used,
                    'peak_memory': profiler.peak_memory,
                    'total_trades': result['total_trades'],
                    'total_return': result['total_return'],
                    'custom_metrics': result['custom_metrics'],
                    'backtest_stats': result['backtest_stats']
                }
            finally:
                # Restore original working directory
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"❌ Reference backtest failed: {e}")
            import traceback
            traceback.print_exc()
            return None


def run_custom_backtest(symbol: str = "NIFTY50", timeframe: int = 30, test_period_days: int = 30):
    """
    Run our custom backtesting engine version.
    
    Args:
        symbol: Symbol to test
        timeframe: Timeframe in minutes
        test_period_days: Number of days to test (for short-term comparison)
        
    Returns:
        Performance results dictionary
    """
    logger.info(f"🔬 Running Custom Backtest (our engine)")
    
    with PerformanceProfiler("Custom Backtest") as profiler:
        try:
            # Import our backtesting modules
            from app.services.backtest_service import BacktestService
            from app.services.backtesting.engine import BacktestEngine
            from app.services.backtesting.simple_price_action_strategy import SimplePriceActionStrategy
            
            # Initialize database connection
            db = next(get_db())
            data_service = OptimizedDataService(db)
            
            # Get data for the same period
            end_date = datetime.now()
            start_date = end_date - timedelta(days=test_period_days) if test_period_days else datetime(2017, 1, 1)
            
            # Fetch data
            data = data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                interval="1m",
                exchange="NSE",
                as_dataframe=True
            )
            
            if data is None or data.empty:
                logger.error(f"❌ No data found for {symbol}")
                return None
            
            # Resample to specified timeframe if needed
            if timeframe > 1:
                rule = f'{timeframe}T'
                resampled_data = pd.DataFrame()
                resampled_data['open'] = data['open'].resample(rule).first()
                resampled_data['high'] = data['high'].resample(rule).max()
                resampled_data['low'] = data['low'].resample(rule).min()
                resampled_data['close'] = data['close'].resample(rule).last()
                resampled_data['volume'] = data['volume'].resample(rule).sum()
                resampled_data.dropna(inplace=True)
                data = resampled_data
            
            # Rename columns to match our engine expectations
            data = data.rename(columns={
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume'
            })
            
            # Add timestamp column
            data['timestamp'] = data.index
            
            logger.info(f"📊 Data shape: {data.shape}")
            
            # Initialize strategy
            strategy = SimplePriceActionStrategy("SimplePriceAction")
            
            # Initialize backtest engine
            engine = BacktestEngine()
            engine.set_strategy(strategy)
            engine.set_data(data)
            
            # Run backtest
            results = engine.run(symbol=symbol)
            
            # Calculate metrics
            total_trades = len(engine.portfolio.closed_positions)
            if engine.portfolio.equity_curve:
                initial_equity = engine.portfolio.equity_curve[0]
                final_equity = engine.portfolio.equity_curve[-1]
                total_return = ((final_equity - initial_equity) / initial_equity) * 100
            else:
                total_return = 0.0
            
            db.close()
            
            return {
                'engine': 'custom_engine',
                'execution_time': profiler.execution_time,
                'memory_used': profiler.memory_used,
                'peak_memory': profiler.peak_memory,
                'total_trades': total_trades,
                'total_return': total_return,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"❌ Custom backtest failed: {e}")
            traceback.print_exc()
            return None


def compare_results(reference_result: dict, custom_result: dict):
    """
    Compare results between reference and custom engines.
    
    Args:
        reference_result: Results from reference engine
        custom_result: Results from custom engine
    """
    logger.info("\n" + "="*80)
    logger.info(" "*25 + "PERFORMANCE COMPARISON RESULTS")
    logger.info("="*80)
    
    if not reference_result or not custom_result:
        logger.error("❌ Cannot compare - one or both tests failed")
        return
    
    # Performance comparison
    logger.info("\n📊 EXECUTION PERFORMANCE:")
    logger.info(f"{'Metric':<25} | {'Reference':<15} | {'Custom':<15} | {'Winner':<10}")
    logger.info("-" * 70)
    
    # Execution time
    ref_time = reference_result['execution_time']
    custom_time = custom_result['execution_time']
    time_winner = "Custom" if custom_time < ref_time else "Reference"
    speedup = ref_time / custom_time if custom_time > 0 else float('inf')
    
    logger.info(f"{'Execution Time (s)':<25} | {ref_time:<15.2f} | {custom_time:<15.2f} | {time_winner:<10}")
    logger.info(f"{'Speedup Factor':<25} | {'-':<15} | {speedup:<15.2f} | {'-':<10}")
    
    # Memory usage
    ref_memory = reference_result['memory_used']
    custom_memory = custom_result['memory_used']
    memory_winner = "Custom" if custom_memory < ref_memory else "Reference"
    
    logger.info(f"{'Memory Used (MB)':<25} | {ref_memory:<15.1f} | {custom_memory:<15.1f} | {memory_winner:<10}")
    
    # Peak memory
    ref_peak = reference_result['peak_memory']
    custom_peak = custom_result['peak_memory']
    peak_winner = "Custom" if custom_peak < ref_peak else "Reference"
    
    logger.info(f"{'Peak Memory (MB)':<25} | {ref_peak:<15.1f} | {custom_peak:<15.1f} | {peak_winner:<10}")
    
    # Trading results comparison
    logger.info("\n📈 TRADING RESULTS:")
    logger.info(f"{'Metric':<25} | {'Reference':<15} | {'Custom':<15} | {'Difference':<15}")
    logger.info("-" * 75)
    
    ref_trades = reference_result['total_trades']
    custom_trades = custom_result['total_trades']
    trade_diff = custom_trades - ref_trades
    
    logger.info(f"{'Total Trades':<25} | {ref_trades:<15} | {custom_trades:<15} | {trade_diff:<15}")
    
    ref_return = reference_result['total_return']
    custom_return = custom_result['total_return']
    return_diff = custom_return - ref_return
    
    logger.info(f"{'Total Return (%)':<25} | {ref_return:<15.2f} | {custom_return:<15.2f} | {return_diff:<15.2f}")
    
    # Overall recommendation
    logger.info("\n🏆 RECOMMENDATION:")
    
    performance_score_ref = 0
    performance_score_custom = 0
    
    if ref_time < custom_time:
        performance_score_ref += 1
    else:
        performance_score_custom += 1
        
    if ref_memory < custom_memory:
        performance_score_ref += 1
    else:
        performance_score_custom += 1
    
    if performance_score_custom > performance_score_ref:
        logger.info("✅ Custom Engine performs better overall")
        recommendation = "custom"
    elif performance_score_ref > performance_score_custom:
        logger.info("✅ Reference Engine (backtesting library) performs better overall")
        recommendation = "reference"
    else:
        logger.info("⚖️  Both engines have similar performance")
        recommendation = "similar"
    
    logger.info("="*80)
    
    return {
        'recommendation': recommendation,
        'speedup_factor': speedup,
        'memory_efficiency': ref_memory / custom_memory if custom_memory > 0 else 1.0,
        'reference_result': reference_result,
        'custom_result': custom_result
    }


def main():
    """Main comparison function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Compare backtesting engine performance')
    parser.add_argument('--symbol', '-s', default='NIFTY50', help='Symbol to test')
    parser.add_argument('--timeframe', '-t', type=int, default=30, help='Timeframe in minutes')
    parser.add_argument('--test-days', '-d', type=int, default=None, 
                       help='Number of days to test (None for full dataset)')
    parser.add_argument('--reference-only', action='store_true', 
                       help='Run only reference backtest')
    parser.add_argument('--custom-only', action='store_true', 
                       help='Run only custom backtest')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting Backtesting Engine Performance Comparison")
    logger.info(f"📊 Symbol: {args.symbol}, Timeframe: {args.timeframe}min")
    if args.test_days:
        logger.info(f"📅 Test period: {args.test_days} days")
    else:
        logger.info(f"📅 Test period: Full dataset")
    
    reference_result = None
    custom_result = None
    
    # Run reference backtest
    if not args.custom_only:
        reference_result = run_reference_backtest(
            symbol=args.symbol,
            timeframe=args.timeframe,
            test_period_days=args.test_days
        )
    
    # Run custom backtest
    if not args.reference_only:
        custom_result = run_custom_backtest(
            symbol=args.symbol,
            timeframe=args.timeframe,
            test_period_days=args.test_days
        )
    
    # Compare results
    if reference_result and custom_result:
        comparison = compare_results(reference_result, custom_result)
        
        # Save results to file
        results = {
            'timestamp': datetime.now().isoformat(),
            'test_parameters': {
                'symbol': args.symbol,
                'timeframe': args.timeframe,
                'test_days': args.test_days
            },
            'comparison': comparison
        }
        
        output_file = f"performance_comparison_{args.symbol}_{args.timeframe}min.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"📄 Results saved to {output_file}")
    
    logger.info("🎉 Performance comparison completed!")


if __name__ == "__main__":
    main()
